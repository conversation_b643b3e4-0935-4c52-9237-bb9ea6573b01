[1, ["86CHtLQoRLkYcH+h9xQG6h@f9941", "e72a9NVA1O+4q3MK0l4xIh", "ff3ACRArVH6Zy6dSrQ9/Jg", "7f46LBRONF7aOkgekKaZTH", "b96V8CSuxMAYNDHGzgNrDG@f9941"], ["node", "destroyedSprite", "paintPrefab", "normalBulletPrefab", "rocketPrefab", "_spriteFrame", "root", "data"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_mobility", "_layer", "_components", "_prefab", "_lscale"], 0, 9, 4, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.RigidBody2D", ["enabledContactListener", "_type", "_gravityScale", "node", "__prefab"], 0, 1, 4], ["cc.BoxCollider2D", ["_friction", "node", "__prefab", "_size"], 2, 1, 4, 5], ["b39243E9/5JHJc2plQZwxL1", ["maxSpeed", "acceleration", "brakeDeceleration", "friction", "paintSprayInterval", "weaponType", "maxAmmo", "node", "__prefab", "destroyedSprite", "paintPrefab", "normalBulletPrefab", "rocketPrefab"], -4, 1, 4, 6, 6, 6, 6], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 3, 4, 5, 4], [2, 0, 1, 2, 1], [4, 0, 1, 2, 3, 4, 4], [5, 0, 1, 2, 3, 2], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 8], [7, 0, 1, 2, 3, 2], [8, 0, 1, 2, 3, 4, 5, 5]], [[1, "car-2"], [2, "car-2", 2, 33554432, [[3, -2, [0, "32Km9yAHFM/pk9eNwISnuA"], [5, 27.8, 60]], [4, true, 1, 0, -3, [0, "69SdxbUcBHGY7VJBch9/Rv"]], [5, 1, -4, [0, "08qNwbYGlGOYMTAXi2toBJ"], [5, 27.8, 60.1]], [6, 25, 25, 25, 1.2, 0.1, 2, 10, -5, [0, "27tTwXETJJeJ7mbQOlMn5u"], 0, 1, 2, 3], [7, 0, -6, [0, "15xxLaEV9KJqB8RfIq85gn"], 4]], [8, "23jWoBtgxKpIkjHligqpM4", null, null, null, -1, 0], [1, 1.1, 1.1, 1]]], 0, [0, 6, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 7, 1, 6], [0, 0, 0, 0, 0], [1, 2, 3, 4, 5], [0, 1, 2, 3, 4]]