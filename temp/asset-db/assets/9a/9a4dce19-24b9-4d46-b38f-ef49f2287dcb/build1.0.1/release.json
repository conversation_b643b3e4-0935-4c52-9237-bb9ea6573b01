[1, ["4c8ZSK/8pCbpxDHnyGShtg@f9941", "59OQTy+oVJj5sOn3mJDCT9@f9941", "1cGPbnOxNAG4WhdGv3KElJ@f9941", "feTFjyizNF45368337JyRD@f9941", "a4JsLIWLFOWqFs51BhotA0@f9941", "6aAXnbsLxJBYlTirB2o8/W@f9941", "1cUFL6iBlGvLuVGqkLhz4u@f9941", "dd2z2XW55MyKzf+eRFOslI@f9941", "83uu73dGtPkZqE/YpY0riz@f9941", "81Ugz1OgJGO7PsyIcKP0lA", "c4kqHHgpBNcZj/pLuCLLBn", "050J0q0eZNtLfxRsgoVh4c", "dfFrs6vHdHZrrnJ2kxbtbl", "9fho2hieRMpqThK1kpgdsh", "c18s46WqhFuqs+X/+LuHr5"], ["node", "_spriteFrame", "_clip", "_normalSprite", "_parent", "asset", "_cameraComponent", "carDriftAudioSource", "carStartAudioSource", "carDestructionAudioSource", "buttonClickAudioSource", "bgmAudioSource", "_target", "root", "scene"], [["cc.Node", ["_name", "_layer", "_id", "_obj<PERSON><PERSON>s", "_active", "__editorExtras__", "_components", "_parent", "_lpos", "_children", "_prefab"], -3, 9, 1, 5, 2, 4], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isBold", "_enableOutline", "_enableShadow", "_enableWrapText", "_overflow", "node", "_color", "_shadowColor", "_outlineColor"], -6, 1, 5, 5, 5], ["cc.Node", ["_name", "_layer", "_id", "_components", "_parent", "_children", "_lpos"], 0, 12, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_spriteFrame", "_color"], 1, 1, 6, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "_normalColor", "_target", "_normalSprite"], 2, 1, 5, 1, 6], ["cc.Widget", ["_alignFlags", "_top", "_bottom", "_right", "_isAbsRight", "_isAbsTop", "_originalHeight", "_alignMode", "node", "_target"], -5, 1, 1], ["cc.AudioSource", ["_loop", "_volume", "_playOnAwake", "node"], 0, 1], ["cc.SceneAsset", ["_name"], 2], ["cc.Scene", ["_name", "autoReleaseAssets", "_children", "_prefab", "_globals"], 1, 2, 4, 4], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["cc.Camera", ["_projection", "_orthoHeight", "_near", "_visibility", "node", "_color"], -1, 1, 5], ["cc.UITransform", ["node", "_contentSize"], 3, 1, 5], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -2, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["cc.TargetInfo", ["localID"], 2], ["cc.<PERSON>", ["_alignCanvasWithScreen", "node", "_cameraComponent"], 2, 1, 1], ["1b6d6aHKXNGGK9721LvZ7jB", ["node"], 3, 1], ["0cf64BckYpA8bODaKn8c5t/", ["node", "startGameBtn", "settingBtn", "closesettingBtn", "audioBtn", "settingPanel", "audioLabel", "helpButton", "closehelpBtn", "helpPanel"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["7c3850Yg29IBK4wW9FYz7f1", ["node", "bgmAudioSource", "buttonClickAudioSource", "carDestructionAudioSource", "carStartAudioSource", "carDriftAudioSource"], 3, 1, 1, 1, 1, 1, 1], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", ["_enabled"], 2], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3]], [[11, 0, 1, 1], [3, 0, 2, 3, 2], [6, 2, 3, 2], [17, 0, 2], [0, 0, 1, 7, 6, 8, 3], [16, 0, 1, 2, 2], [0, 0, 3, 1, 7, 6, 4], [2, 0, 1, 4, 3, 6, 3], [3, 1, 0, 2, 4, 3, 3], [4, 0, 1, 2, 3, 2], [4, 0, 1, 2], [0, 0, 4, 1, 7, 9, 6, 8, 4], [0, 0, 2, 7, 6, 3], [2, 0, 1, 4, 5, 3, 6, 3], [7, 0, 2], [8, 0, 1, 2, 3, 4, 3], [0, 0, 1, 2, 9, 6, 8, 4], [0, 0, 1, 7, 6, 3], [0, 0, 1, 7, 9, 6, 8, 3], [0, 3, 5, 7, 10, 3], [9, 0, 1, 2, 3, 2], [2, 0, 1, 4, 5, 3, 3], [2, 0, 1, 4, 3, 3], [2, 0, 2, 3, 3], [10, 0, 1, 2, 3, 4, 5, 5], [3, 1, 0, 2, 3, 3], [3, 2, 3, 1], [1, 0, 1, 2, 3, 7, 4, 5, 6, 9, 10, 9], [1, 0, 1, 2, 3, 7, 4, 5, 6, 9, 10, 12, 11, 9], [1, 0, 1, 2, 3, 7, 4, 5, 6, 9, 10, 11, 9], [1, 0, 1, 2, 3, 9, 10, 5], [1, 0, 1, 2, 4, 5, 6, 9, 10, 7], [1, 0, 1, 2, 3, 8, 4, 5, 6, 9, 10, 9], [4, 0, 1, 2, 3, 4, 2], [5, 0, 3, 1, 2, 4, 5, 6, 8, 9, 8], [5, 0, 1, 2, 7, 8, 5], [12, 0, 1, 2, 3, 4, 5, 4], [13, 0, 1, 2, 3, 4, 5, 6], [14, 0, 1, 2, 3], [15, 0, 1, 2, 3], [18, 0, 1, 2, 2], [19, 0, 1], [20, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [21, 0, 1, 2, 3, 4, 5, 1], [6, 0, 1, 3, 3], [22, 0, 1, 2, 3, 4, 5, 6, 7, 1], [23, 0, 1, 1], [24, 0, 1, 1], [25, 1], [26, 1], [27, 1], [28, 0, 2], [29, 1], [30, 1]], [[14, "mainmenu"], [16, "<PERSON><PERSON>", 33554432, "beI88Z2HpFELqR4T5EMHpg", [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15], [[0, -1, [5, 1280, 720]], [40, false, -3, -2], [35, 45, 5.684341886080802e-14, 5.684341886080802e-14, 1, -4]], [1, 640, 360.00000000000006, 0]], [23, "SoundManager", "f8zc0C52RFyJSZjqngqV7J", [[[43, -21, -20, -19, -18, -17, -16], -22, -23, -24, -25, -26, [2, false, -27], [2, false, -28]], 4, 1, 1, 1, 1, 1, 4, 4]], [11, "HelpPanel", false, 33554432, 1, [-31, -32, -33], [[0, -29, [5, 850.965, 702.41]], [1, 0, -30, 12]], [1, 21.627, 0.529, 0]], [13, "startButton", 33554432, 1, [-37], [[[0, -34, [5, 248.865, 71.20889379286326]], [8, 1, 0, -35, [4, 4293519859], 2], -36], 4, 4, 1], [1, 0.9, -80.462, 0]], [18, "resetButton", 33554432, 1, [-42], [[0, -38, [5, 257.85, 73.77981341084441]], [8, 1, 0, -39, [4, 4293519859], 3], [33, 3, -41, [4, 4292269782], -40, 4]], [1, 2.472, -164.535, 0]], [13, "helpButton", 33554432, 1, [-46], [[[0, -43, [5, 257.85, 73.77981341084441]], [8, 1, 0, -44, [4, 4293519859], 5], -45], 4, 4, 1], [1, 2.472, -254.486, 0]], [7, "settingButton", 33554432, 1, [[[0, -47, [5, 66.17800000000001, 67.19247971371924]], [25, 1, 0, -48, 6], -49, [34, 33, 0.026357031250000166, 0.04493994464325066, 335, false, false, 50, -50, 1]], 4, 4, 1, 4], [1, 573.1739999999998, 294.0469999999999, 0]], [11, "SettingPanel", false, 33554432, 1, [-53, -54], [[0, -51, [5, 509, 558]], [26, -52, 10]], [1, 0.8537647541830893, -30.046650789554917, 0]], [15, "mainmenu", true, [1, -56, -57, 2], [37, null, null, "9a4dce19-24b9-4d46-b38f-ef49f2287dcb", null, null, [-55]], [45, [46, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [47, [4, 4283190348], [0, 512, 512]], [48], [49], [50], [51, false], [52], [53]]], [21, "audioButton", 33554432, 8, [-61], [[[0, -58, [5, 280.995, 81.87200000000001]], [1, 0, -59, 9], -60], 4, 4, 1]], [7, "close", 33554432, 8, [[[0, -62, [5, 55.50500000000002, 55.50500000000002]], [1, 0, -63, 8], -64], 4, 4, 1], [1, 212.483, 235.404, 0]], [7, "close", 33554432, 3, [[[0, -65, [5, 55.50500000000002, 55.50500000000002]], [1, 0, -66, 11], -67], 4, 4, 1], [1, 367.486, 300.922, 0]], [17, "background", 33554432, 1, [[0, -68, [5, 1650.8790000000001, 927.8346875]], [1, 0, -69, 0]]], [4, "logo", 33554432, 1, [[0, -70, [5, 488.76500000000004, 357.68999999999994]], [1, 0, -71, 1]], [1, -22.533000000000015, 181.15499999999992, 0]], [6, "Label", 512, 33554432, 4, [[0, -72, [5, 219.0244140625, 41.8]], [27, "单人游戏 single", 30, 30, 30, false, true, true, true, -73, [4, 4282203594]]]], [6, "Label", 512, 33554432, 5, [[0, -74, [5, 205.6357421875, 41.8]], [28, "尽情期待 to do", 30, 30, 30, false, true, true, true, -75, [4, 4284079342], [4, 4278192908], [4, 4278394139]]]], [6, "Label", 512, 33554432, 6, [[0, -76, [5, 194.0048828125, 41.8]], [29, "游戏说明 help", 30, 30, 30, false, true, true, true, -77, [4, 4288536406], [4, 4278588938]]]], [4, "traffic", 33554432, 1, [[0, -78, [5, 124.47400000000002, 298.7376]], [1, 0, -79, 7]], [1, -190.725, -174.653, 0]], [22, "Label", 33554432, 10, [[[0, -80, [5, 91.19140625, 56.047999999999995]], -81], 4, 1]], [4, "title", 33554432, 3, [[0, -82, [5, 188.173583984375, 54.4]], [31, "help/帮助", 42.5, 42.5, true, true, true, -83, [4, 4286765690]]], [1, -7.99, 302.016, 0]], [4, "content", 33554432, 3, [[0, -84, [5, 779.6208437500001, 473.404]], [32, "不同的车辆拥有不同特性和武器，用出色的驾驶和甩尾技术在有限的时间内喷洒更多颜料！\n必要时，可使用车辆的专属武器攻击对手/移除对手已喷洒的燃料\n倒计时结束后，依据玩家颜料占比进行评分\nA：45% B：35% C：25% F：<25%或玩家车辆摧毁\nDifferent cars have different features and weapons. \nUse your excellent driving and drifting skills \nto spray more paint within the limited time! \nWhen necessary, use the exclusive weapon to attack your opponents or remove the fuel they have sprayed.\nAfter the countdown ends, players will be scored based on their proportion of paint. \nA: 45% B: 35% C: 25% F: < 25% or player's vehicle destroyed", 25.6, 25.6, 35.4, 3, true, true, true, -85, [4, 4292519892]]], [1, 5.428, -21.068, 0]], [19, 0, {}, 1, [36, "98m1scCCpHFaAD50RjkFIZ", null, null, -86, [38, "3bBm5N8+NLeYdVfjOnRTcE", null, [[39, "loading", ["_name"], [3, ["98m1scCCpHFaAD50RjkFIZ"]]], [5, ["_lpos"], [3, ["98m1scCCpHFaAD50RjkFIZ"]], [1, -0.0004999999999597549, 0, 0]], [5, ["_lrot"], [3, ["98m1scCCpHFaAD50RjkFIZ"]], [3, 0, 0, 0, 1]], [5, ["_euler"], [3, ["98m1scCCpHFaAD50RjkFIZ"]], [1, 0, 0, 0]], [5, ["_contentSize"], [3, ["dftExsNEZHn4dtPomyMxdT"]], [5, 1650.8790000000001, 927.834]]]], 13]], [20, "Camera", 1, [-87], [1, 0, 0, 1000]], [24, 0, 360, 0, 1108344832, 23, [4, 4278190080]], [9, 3, 4, [4, 4292269782], 4], [9, 3, 6, [4, 4292269782], 6], [9, 3, 7, [4, 4292269782], 7], [10, 3, 11], [30, "音效:开\nSound: on", 20, 20, 24.8, 19, [4, 4280967838]], [10, 3, 10], [10, 3, 12], [12, "<PERSON><PERSON><PERSON><PERSON>", "ef0ZsUXShJ9pBKMQh7xWv7", 9, [[41, -88]]], [12, "MainMenuController", "1eIGv73uxJ149Ou4e8BJTt", 9, [[42, -89, 25, 27, 28, 30, 8, 29, 26, 31, 3]]], [44, true, 0.5, 2], [2, false, 2], [2, false, 2], [2, false, 2], [2, false, 2]], 0, [0, 0, 1, 0, 6, 24, 0, 0, 1, 0, 0, 1, 0, -1, 23, 0, -2, 13, 0, -3, 14, 0, -4, 4, 0, -5, 5, 0, -6, 6, 0, -7, 7, 0, -8, 18, 0, -9, 8, 0, -10, 3, 0, -11, 22, 0, 7, 38, 0, 8, 37, 0, 9, 36, 0, 10, 35, 0, 11, 34, 0, 0, 2, 0, -2, 34, 0, -3, 35, 0, -4, 37, 0, -5, 38, 0, -6, 36, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, -1, 12, 0, -2, 20, 0, -3, 21, 0, 0, 4, 0, 0, 4, 0, -3, 25, 0, -1, 15, 0, 0, 5, 0, 0, 5, 0, 12, 5, 0, 0, 5, 0, -1, 16, 0, 0, 6, 0, 0, 6, 0, -3, 26, 0, -1, 17, 0, 0, 7, 0, 0, 7, 0, -3, 27, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, -1, 11, 0, -2, 10, 0, -1, 22, 0, -2, 32, 0, -3, 33, 0, 0, 10, 0, 0, 10, 0, -3, 30, 0, -1, 19, 0, 0, 11, 0, 0, 11, 0, -3, 28, 0, 0, 12, 0, 0, 12, 0, -3, 31, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -2, 29, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 13, 22, 0, -1, 24, 0, 0, 32, 0, 0, 33, 0, 14, 9, 1, 4, 9, 2, 4, 9, 89], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 26, 27, 34, 35, 36, 37, 38], [1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 5, 3, 3, 3, 2, 2, 2, 2, 2], [6, 7, 0, 1, 1, 2, 3, 8, 4, 0, 5, 4, 5, 9, 0, 2, 3, 10, 11, 12, 13, 14]]