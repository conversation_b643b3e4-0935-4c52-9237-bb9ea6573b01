[1, 0, 0, [["cc.EffectAsset", ["_name", "combinations", "shaders", "techniques"], -1]], [[0, 0, 1, 2, 3, 5]], [[0, "internal/builtin-clear-stencil", [{}], [{"hash": 3507038093, "name": "internal/builtin-clear-stencil|sprite-vs:vert|sprite-fs:frag", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\nin vec3 a_position;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 frag () {\n  vec4 o = vec4(1.0);\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\nattribute vec3 a_position;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 frag () {\n  vec4 o = vec4(1.0);\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 0, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 0}}, "defines": []}], [{"passes": [{"program": "internal/builtin-clear-stencil|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}]]], 0, 0, [], [], []]