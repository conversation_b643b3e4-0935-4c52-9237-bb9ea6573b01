2025-8-12 23:14:16-debug: start **** info
2025-8-12 23:14:16-log: Cannot access game frame or container.
2025-8-12 23:14:16-debug: asset-db:require-engine-code (411ms)
2025-8-12 23:14:16-log: meshopt wasm decoder initialized
2025-8-12 23:14:16-log: [bullet]:bullet wasm lib loaded.
2025-8-12 23:14:16-log: [box2d]:box2d wasm lib loaded.
2025-8-12 23:14:16-log: Cocos Creator v3.8.6
2025-8-12 23:14:16-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:29.50MB, end 79.93MB, increase: 50.43MB
2025-8-12 23:14:16-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.77MB, end 88.45MB, increase: 7.68MB
2025-8-12 23:14:17-debug: [Assets Memory track]: asset-db-plugin-register: builder start:88.47MB, end 229.07MB, increase: 140.60MB
2025-8-12 23:14:17-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:224.06MB, end 226.85MB, increase: 2.78MB
2025-8-12 23:14:17-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.64MB, end 227.01MB, increase: 146.37MB
2025-8-12 23:14:17-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:79.95MB, end 227.59MB, increase: 147.65MB
2025-8-12 23:14:16-log: Using legacy pipeline
2025-8-12 23:14:16-log: Forward render pipeline initialized.
2025-8-12 23:14:17-debug: run package(migu-mini-game) handler(enable) start
2025-8-12 23:14:17-debug: run package(ohos) handler(enable) start
2025-8-12 23:14:17-debug: run package(ohos) handler(enable) success!
2025-8-12 23:14:17-debug: run package(oppo-mini-game) handler(enable) start
2025-8-12 23:14:17-debug: run package(oppo-mini-game) handler(enable) success!
2025-8-12 23:14:17-debug: run package(runtime-dev-tools) handler(enable) start
2025-8-12 23:14:17-debug: run package(runtime-dev-tools) handler(enable) success!
2025-8-12 23:14:17-debug: run package(migu-mini-game) handler(enable) success!
2025-8-12 23:14:17-debug: run package(taobao-mini-game) handler(enable) success!
2025-8-12 23:14:17-debug: run package(vivo-mini-game) handler(enable) start
2025-8-12 23:14:17-debug: run package(native) handler(enable) success!
2025-8-12 23:14:17-debug: run package(web-desktop) handler(enable) start
2025-8-12 23:14:17-debug: run package(web-desktop) handler(enable) success!
2025-8-12 23:14:17-debug: run package(web-mobile) handler(enable) success!
2025-8-12 23:14:17-debug: run package(web-mobile) handler(enable) start
2025-8-12 23:14:17-debug: run package(wechatgame) handler(enable) start
2025-8-12 23:14:17-debug: run package(wechatgame) handler(enable) success!
2025-8-12 23:14:17-debug: run package(wechatprogram) handler(enable) start
2025-8-12 23:14:17-debug: run package(wechatprogram) handler(enable) success!
2025-8-12 23:14:17-debug: run package(windows) handler(enable) success!
2025-8-12 23:14:17-debug: run package(taobao-mini-game) handler(enable) start
2025-8-12 23:14:17-debug: run package(windows) handler(enable) start
2025-8-12 23:14:17-debug: run package(xiaomi-quick-game) handler(enable) start
2025-8-12 23:14:17-debug: run package(cocos-service) handler(enable) start
2025-8-12 23:14:17-debug: run package(cocos-service) handler(enable) success!
2025-8-12 23:14:17-debug: run package(im-plugin) handler(enable) start
2025-8-12 23:14:17-debug: run package(im-plugin) handler(enable) success!
2025-8-12 23:14:17-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-8-12 23:14:17-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-8-12 23:14:17-debug: run package(placeholder) handler(enable) start
2025-8-12 23:14:17-debug: run package(placeholder) handler(enable) success!
2025-8-12 23:14:17-debug: asset-db:worker-init: initPlugin (761ms)
2025-8-12 23:14:17-debug: run package(native) handler(enable) start
2025-8-12 23:14:17-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-8-12 23:14:17-debug: run package(vivo-mini-game) handler(enable) success!
2025-8-12 23:14:17-debug: [Assets Memory track]: asset-db:worker-init start:29.49MB, end 228.21MB, increase: 198.73MB
2025-8-12 23:14:17-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-12 23:14:17-debug: Run asset db hook programming:beforePreStart ...
2025-8-12 23:14:17-debug: Run asset db hook programming:beforePreStart success!
2025-8-12 23:14:17-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-12 23:14:17-debug: asset-db:worker-init (1269ms)
2025-8-12 23:14:17-debug: asset-db-hook-programming-beforePreStart (50ms)
2025-8-12 23:14:17-debug: asset-db-hook-engine-extends-beforePreStart (50ms)
2025-8-12 23:14:17-debug: Preimport db internal success
2025-8-12 23:14:17-debug: Preimport db assets success
2025-8-12 23:14:17-debug: Run asset db hook programming:afterPreStart ...
2025-8-12 23:14:17-debug: starting packer-driver...
2025-8-12 23:14:17-debug: initialize scripting environment...
2025-8-12 23:14:17-debug: [[Executor]] prepare before lock
2025-8-12 23:14:17-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-12 23:14:17-debug: [[Executor]] prepare after unlock
2025-8-12 23:14:17-debug: Start up the 'internal' database...
2025-8-12 23:14:17-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-12 23:14:17-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-12 23:14:17-debug: Run asset db hook programming:afterPreStart success!
2025-8-12 23:14:17-debug: asset-db-hook-programming-afterPreStart (288ms)
2025-8-12 23:14:17-debug: asset-db:worker-effect-data-processing (152ms)
2025-8-12 23:14:17-debug: asset-db-hook-engine-extends-afterPreStart (152ms)
2025-8-12 23:14:17-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:228.30MB, end 242.72MB, increase: 14.41MB
2025-8-12 23:14:17-debug: Start up the 'assets' database...
2025-8-12 23:14:17-debug: asset-db:worker-startup-database[internal] (415ms)
2025-8-12 23:14:17-debug: [Assets Memory track]: asset-db:worker-startup-database[assets] start:230.85MB, end 240.80MB, increase: 9.95MB
2025-8-12 23:14:17-debug: lazy register asset handler text
2025-8-12 23:14:17-debug: lazy register asset handler json
2025-8-12 23:14:17-debug: lazy register asset handler spine-data
2025-8-12 23:14:17-debug: lazy register asset handler dragonbones
2025-8-12 23:14:17-debug: lazy register asset handler dragonbones-atlas
2025-8-12 23:14:17-debug: lazy register asset handler terrain
2025-8-12 23:14:17-debug: lazy register asset handler javascript
2025-8-12 23:14:17-debug: lazy register asset handler typescript
2025-8-12 23:14:17-debug: lazy register asset handler scene
2025-8-12 23:14:17-debug: lazy register asset handler prefab
2025-8-12 23:14:17-debug: lazy register asset handler sprite-frame
2025-8-12 23:14:17-debug: lazy register asset handler tiled-map
2025-8-12 23:14:17-debug: lazy register asset handler buffer
2025-8-12 23:14:17-debug: lazy register asset handler image
2025-8-12 23:14:17-debug: [Assets Memory track]: asset-db:worker-init: startup start:227.96MB, end 240.81MB, increase: 12.85MB
2025-8-12 23:14:17-debug: lazy register asset handler alpha-image
2025-8-12 23:14:17-debug: lazy register asset handler sign-image
2025-8-12 23:14:17-debug: lazy register asset handler texture-cube
2025-8-12 23:14:17-debug: lazy register asset handler render-texture
2025-8-12 23:14:17-debug: lazy register asset handler texture
2025-8-12 23:14:17-debug: lazy register asset handler texture-cube-face
2025-8-12 23:14:17-debug: lazy register asset handler rt-sprite-frame
2025-8-12 23:14:17-debug: lazy register asset handler directory
2025-8-12 23:14:17-debug: lazy register asset handler *
2025-8-12 23:14:17-debug: lazy register asset handler gltf
2025-8-12 23:14:17-debug: lazy register asset handler gltf-skeleton
2025-8-12 23:14:17-debug: lazy register asset handler gltf-scene
2025-8-12 23:14:17-debug: lazy register asset handler gltf-material
2025-8-12 23:14:17-debug: lazy register asset handler fbx
2025-8-12 23:14:17-debug: lazy register asset handler erp-texture-cube
2025-8-12 23:14:17-debug: lazy register asset handler material
2025-8-12 23:14:17-debug: lazy register asset handler physics-material
2025-8-12 23:14:17-debug: lazy register asset handler effect
2025-8-12 23:14:17-debug: lazy register asset handler effect-header
2025-8-12 23:14:17-debug: lazy register asset handler audio-clip
2025-8-12 23:14:17-debug: lazy register asset handler animation-clip
2025-8-12 23:14:17-debug: lazy register asset handler animation-graph
2025-8-12 23:14:17-debug: lazy register asset handler animation-graph-variant
2025-8-12 23:14:17-debug: lazy register asset handler animation-mask
2025-8-12 23:14:17-debug: lazy register asset handler gltf-mesh
2025-8-12 23:14:17-debug: lazy register asset handler bitmap-font
2025-8-12 23:14:17-debug: lazy register asset handler particle
2025-8-12 23:14:17-debug: lazy register asset handler auto-atlas
2025-8-12 23:14:17-debug: lazy register asset handler gltf-animation
2025-8-12 23:14:17-debug: lazy register asset handler label-atlas
2025-8-12 23:14:17-debug: lazy register asset handler gltf-embeded-image
2025-8-12 23:14:17-debug: lazy register asset handler sprite-atlas
2025-8-12 23:14:17-debug: lazy register asset handler render-stage
2025-8-12 23:14:17-debug: lazy register asset handler instantiation-material
2025-8-12 23:14:17-debug: lazy register asset handler instantiation-mesh
2025-8-12 23:14:17-debug: lazy register asset handler instantiation-skeleton
2025-8-12 23:14:17-debug: lazy register asset handler render-pipeline
2025-8-12 23:14:17-debug: lazy register asset handler video-clip
2025-8-12 23:14:17-debug: lazy register asset handler render-flow
2025-8-12 23:14:17-debug: lazy register asset handler ttf-font
2025-8-12 23:14:17-debug: lazy register asset handler instantiation-animation
2025-8-12 23:14:17-debug: asset-db:worker-startup-database[assets] (374ms)
2025-8-12 23:14:17-debug: asset-db:start-database (442ms)
2025-8-12 23:14:17-debug: asset-db:ready (2916ms)
2025-8-12 23:14:17-debug: fix the bug of updateDefaultUserData
2025-8-12 23:14:17-debug: init worker message success
2025-8-12 23:14:17-debug: programming:execute-script (3ms)
2025-8-12 23:14:18-debug: [Build Memory track]: builder:worker-init start:243.87MB, end 255.46MB, increase: 11.59MB
2025-8-12 23:14:18-debug: builder:worker-init (201ms)
2025-8-13 00:09:34-debug: refresh db internal success
2025-8-13 00:09:34-debug: refresh db assets success
2025-8-13 00:09:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 00:09:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 00:09:34-debug: asset-db:refresh-all-database (54ms)
2025-8-13 00:09:34-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 00:09:34-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-8-13 21:02:12-debug: refresh db internal success
2025-8-13 21:02:12-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:02:12-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:02:12-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:02:12-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:02:12-debug: refresh db assets success
2025-8-13 21:02:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:02:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:02:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 21:02:12-debug: asset-db:refresh-all-database (60ms)
2025-8-13 21:02:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:02:15-debug: Query all assets info in project
2025-8-13 21:02:15-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:02:15-debug: Skip compress image, progress: 0%
2025-8-13 21:02:15-debug: Init all bundles start..., progress: 0%
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:02:15-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:02:15-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:02:15-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:02:15-debug:   Number of all scripts: 28
2025-8-13 21:02:15-debug:   Number of other assets: 579
2025-8-13 21:02:15-debug:   Number of all scenes: 3
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-13 21:02:15-debug: [Build Memory track]: 查询 Asset Bundle start:198.90MB, end 198.99MB, increase: 95.69KB
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 5%
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-8-13 21:02:15-debug: [Build Memory track]: 查询 Asset Bundle start:199.02MB, end 199.13MB, increase: 116.25KB
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-13 21:02:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.19MB, end 199.20MB, increase: 18.29KB
2025-8-13 21:02:15-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:02:15-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:02:15-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.24MB, end 199.26MB, increase: 18.34KB
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-8-13 21:02:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.29MB, end 199.44MB, increase: 157.34KB
2025-8-13 21:02:15-debug: Query all assets info in project
2025-8-13 21:02:15-debug: Query all assets info in project
2025-8-13 21:02:15-debug: Query all assets info in project
2025-8-13 21:02:15-debug: Query all assets info in project
2025-8-13 21:02:15-debug: Skip compress image, progress: 0%
2025-8-13 21:02:15-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:02:15-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:02:15-debug: Skip compress image, progress: 0%
2025-8-13 21:02:15-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:02:15-debug: Skip compress image, progress: 0%
2025-8-13 21:02:15-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:02:15-debug: Skip compress image, progress: 0%
2025-8-13 21:02:15-debug: Init all bundles start..., progress: 0%
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:02:15-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:02:15-debug: Init all bundles start..., progress: 0%
2025-8-13 21:02:15-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:02:15-debug: Init all bundles start..., progress: 0%
2025-8-13 21:02:15-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:02:15-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:02:15-debug: Init all bundles start..., progress: 0%
2025-8-13 21:02:15-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:02:15-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:02:15-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:02:15-debug:   Number of all scripts: 28
2025-8-13 21:02:15-debug:   Number of other assets: 579
2025-8-13 21:02:15-debug:   Number of all scenes: 3
2025-8-13 21:02:15-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:02:15-debug:   Number of other assets: 579
2025-8-13 21:02:15-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:02:15-debug:   Number of all scenes: 3
2025-8-13 21:02:15-debug:   Number of all scripts: 28
2025-8-13 21:02:15-debug:   Number of other assets: 579
2025-8-13 21:02:15-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-8-13 21:02:15-debug: [Build Memory track]: 查询 Asset Bundle start:199.80MB, end 200.14MB, increase: 351.11KB
2025-8-13 21:02:15-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-debug:   Number of all scenes: 3
2025-8-13 21:02:15-debug:   Number of all scripts: 28
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-8-13 21:02:15-debug: [Build Memory track]: 查询 Asset Bundle start:200.17MB, end 199.43MB, increase: -760.27KB
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-debug: [Build Memory track]: 查询 Asset Bundle start:199.46MB, end 199.48MB, increase: 13.31KB
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:02:15-debug:   Number of all scenes: 3
2025-8-13 21:02:15-debug:   Number of all scripts: 28
2025-8-13 21:02:15-debug:   Number of other assets: 579
2025-8-13 21:02:15-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-8-13 21:02:15-debug: [Build Memory track]: 查询 Asset Bundle start:200.12MB, end 200.49MB, increase: 384.62KB
2025-8-13 21:02:15-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:02:15-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-8-13 21:02:15-debug: [Build Memory track]: 查询 Asset Bundle start:200.54MB, end 200.75MB, increase: 215.12KB
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 21:02:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:200.86MB, end 200.98MB, increase: 129.23KB
2025-8-13 21:02:15-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:02:15-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 21:02:15-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 21:02:15-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:02:15-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:02:15-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:02:15-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:02:15-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.62MB, end 199.64MB, increase: 19.04KB
2025-8-13 21:02:15-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:02:15-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-13 21:02:15-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:02:15-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:02:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.80MB, end 199.83MB, increase: 31.24KB
2025-8-13 21:02:15-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:02:15-debug: [Build Memory track]: 填充脚本数据到 settings.json start:199.86MB, end 199.89MB, increase: 25.70KB
2025-8-13 21:02:15-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:02:15-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-8-13 21:02:15-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:199.92MB, end 200.22MB, increase: 310.07KB
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-8-13 21:02:15-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-8-13 21:04:50-debug: refresh db internal success
2025-8-13 21:04:50-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:04:50-debug: refresh db assets success
2025-8-13 21:04:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:04:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:04:50-debug: asset-db:refresh-all-database (50ms)
2025-8-13 21:04:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:05:23-debug: refresh db internal success
2025-8-13 21:05:23-debug: refresh db assets success
2025-8-13 21:05:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:05:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:05:23-debug: asset-db:refresh-all-database (25ms)
2025-8-13 21:05:42-debug: refresh db internal success
2025-8-13 21:05:42-debug: refresh db assets success
2025-8-13 21:05:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:05:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:05:42-debug: asset-db:refresh-all-database (26ms)
2025-8-13 21:05:42-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:06:49-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:06:49-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (9ms)
2025-8-13 21:07:08-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:08-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (3ms)
2025-8-13 21:07:08-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:08-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (2ms)
2025-8-13 21:07:08-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:08-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (2ms)
2025-8-13 21:07:08-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:08-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (3ms)
2025-8-13 21:07:10-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:10-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (3ms)
2025-8-13 21:07:24-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:24-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (4ms)
2025-8-13 21:07:24-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:24-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (1ms)
2025-8-13 21:07:24-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:24-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (3ms)
2025-8-13 21:07:24-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:24-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (2ms)
2025-8-13 21:07:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:25-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (2ms)
2025-8-13 21:07:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:25-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (3ms)
2025-8-13 21:07:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:25-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (4ms)
2025-8-13 21:07:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:25-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (1ms)
2025-8-13 21:07:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:26-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (4ms)
2025-8-13 21:07:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:26-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (5ms)
2025-8-13 21:07:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:26-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (2ms)
2025-8-13 21:07:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:26-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (2ms)
2025-8-13 21:07:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:26-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (2ms)
2025-8-13 21:07:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/rocket.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:27-debug: asset-db:reimport-asset7fe3a2c1-44e3-45ed-a3a4-81e90a6994c7 (3ms)
2025-8-13 21:07:33-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/bulletexplosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:33-debug: asset-db:reimport-asset124515d6-c01d-4b24-a978-eefc47e30c4d (4ms)
2025-8-13 21:07:33-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/bulletexplosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:33-debug: asset-db:reimport-asset124515d6-c01d-4b24-a978-eefc47e30c4d (2ms)
2025-8-13 21:07:33-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/bulletexplosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:33-debug: asset-db:reimport-asset124515d6-c01d-4b24-a978-eefc47e30c4d (1ms)
2025-8-13 21:07:33-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/bulletexplosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:33-debug: asset-db:reimport-asset124515d6-c01d-4b24-a978-eefc47e30c4d (2ms)
2025-8-13 21:07:33-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/bulletexplosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:33-debug: asset-db:reimport-asset124515d6-c01d-4b24-a978-eefc47e30c4d (2ms)
2025-8-13 21:07:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:45-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (9ms)
2025-8-13 21:07:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:45-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (2ms)
2025-8-13 21:07:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:45-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (2ms)
2025-8-13 21:07:45-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:45-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (3ms)
2025-8-13 21:07:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:46-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (2ms)
2025-8-13 21:07:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:46-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (2ms)
2025-8-13 21:07:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:46-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (4ms)
2025-8-13 21:07:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:07:46-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (3ms)
2025-8-13 21:07:49-debug: refresh db internal success
2025-8-13 21:07:49-debug: refresh db assets success
2025-8-13 21:07:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:07:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:07:49-debug: asset-db:refresh-all-database (42ms)
2025-8-13 21:07:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 21:07:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:07:49-debug: Query all assets info in project
2025-8-13 21:07:49-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:07:49-debug: Skip compress image, progress: 0%
2025-8-13 21:07:49-debug: Init all bundles start..., progress: 0%
2025-8-13 21:07:49-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:07:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:07:49-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:07:49-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:07:49-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:07:49-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:07:49-debug:   Number of all scenes: 3
2025-8-13 21:07:49-debug:   Number of all scripts: 28
2025-8-13 21:07:49-debug:   Number of other assets: 579
2025-8-13 21:07:49-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-8-13 21:07:49-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:07:49-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-8-13 21:07:49-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:07:49-debug: [Build Memory track]: 查询 Asset Bundle start:205.26MB, end 205.25MB, increase: -17.59KB
2025-8-13 21:07:49-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-8-13 21:07:49-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-8-13 21:07:49-debug: [Build Memory track]: 查询 Asset Bundle start:205.44MB, end 205.41MB, increase: -38.38KB
2025-8-13 21:07:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:07:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:07:49-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-13 21:07:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.44MB, end 205.46MB, increase: 23.95KB
2025-8-13 21:07:49-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:07:49-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:07:49-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:07:49-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:07:49-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.49MB, end 205.51MB, increase: 18.47KB
2025-8-13 21:07:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:07:49-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-13 21:07:49-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.54MB, end 205.67MB, increase: 130.83KB
2025-8-13 21:07:49-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-8-13 21:08:29-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:08:29-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (3ms)
2025-8-13 21:08:29-debug: Query all assets info in project
2025-8-13 21:08:29-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:08:29-debug: Skip compress image, progress: 0%
2025-8-13 21:08:29-debug: Init all bundles start..., progress: 0%
2025-8-13 21:08:29-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:08:29-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:08:29-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:08:29-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:08:29-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:08:29-debug:   Number of all scenes: 3
2025-8-13 21:08:29-debug:   Number of all scripts: 28
2025-8-13 21:08:29-debug:   Number of other assets: 579
2025-8-13 21:08:29-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:08:29-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-8-13 21:08:29-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:08:29-debug: [Build Memory track]: 查询 Asset Bundle start:205.49MB, end 205.46MB, increase: -30.84KB
2025-8-13 21:08:29-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:08:29-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-8-13 21:08:29-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-8-13 21:08:29-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-8-13 21:08:29-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:08:29-debug: [Build Memory track]: 查询 Asset Bundle start:205.49MB, end 205.61MB, increase: 119.92KB
2025-8-13 21:08:29-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:08:29-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-13 21:08:29-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.63MB, end 205.65MB, increase: 16.05KB
2025-8-13 21:08:29-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:08:29-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:08:29-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:08:29-debug: [Build Memory track]: 填充脚本数据到 settings.json start:205.67MB, end 205.69MB, increase: 15.32KB
2025-8-13 21:08:29-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:08:29-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:08:29-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 21:08:29-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-8-13 21:08:29-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:205.71MB, end 205.83MB, increase: 122.73KB
2025-8-13 21:08:44-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:08:44-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (1ms)
2025-8-13 21:08:45-debug: Query all assets info in project
2025-8-13 21:08:45-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:08:45-debug: Skip compress image, progress: 0%
2025-8-13 21:08:45-debug: Init all bundles start..., progress: 0%
2025-8-13 21:08:45-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:08:45-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:08:45-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:08:45-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:08:45-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:08:45-debug:   Number of all scenes: 3
2025-8-13 21:08:45-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:08:45-debug:   Number of other assets: 579
2025-8-13 21:08:45-debug:   Number of all scripts: 28
2025-8-13 21:08:45-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-8-13 21:08:45-debug: [Build Memory track]: 查询 Asset Bundle start:206.08MB, end 205.99MB, increase: -97.21KB
2025-8-13 21:08:45-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:08:45-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:08:45-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-8-13 21:08:45-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-8-13 21:08:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:08:45-debug: [Build Memory track]: 查询 Asset Bundle start:206.01MB, end 206.12MB, increase: 110.13KB
2025-8-13 21:08:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:08:45-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-13 21:08:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.15MB, end 206.16MB, increase: 15.41KB
2025-8-13 21:08:45-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:08:45-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:08:45-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:08:45-debug: [Build Memory track]: 填充脚本数据到 settings.json start:206.19MB, end 206.20MB, increase: 15.02KB
2025-8-13 21:08:45-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:08:45-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:08:45-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-8-13 21:08:45-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.23MB, end 205.96MB, increase: -267.07KB
2025-8-13 21:08:57-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/explosion/explosion.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:08:57-debug: asset-db:reimport-assete107910c-0ef8-4a6b-8eb5-46d2b0965f23 (5ms)
2025-8-13 21:09:11-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:09:11-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (6ms)
2025-8-13 21:09:12-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:09:12-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (2ms)
2025-8-13 21:09:12-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:09:12-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (2ms)
2025-8-13 21:09:12-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:09:12-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (2ms)
2025-8-13 21:09:13-debug: Query all assets info in project
2025-8-13 21:09:13-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:09:13-debug: Skip compress image, progress: 0%
2025-8-13 21:09:13-debug: Init all bundles start..., progress: 0%
2025-8-13 21:09:13-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:09:13-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:09:13-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:09:13-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:09:13-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:09:13-debug:   Number of other assets: 579
2025-8-13 21:09:13-debug:   Number of all scenes: 3
2025-8-13 21:09:13-debug:   Number of all scripts: 28
2025-8-13 21:09:13-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:09:13-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-8-13 21:09:13-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 5%
2025-8-13 21:09:13-debug: [Build Memory track]: 查询 Asset Bundle start:206.68MB, end 206.28MB, increase: -411.36KB
2025-8-13 21:09:13-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:09:13-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:09:13-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:09:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:09:13-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-8-13 21:09:13-debug: [Build Memory track]: 查询 Asset Bundle start:206.30MB, end 206.42MB, increase: 119.94KB
2025-8-13 21:09:13-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-13 21:09:13-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:09:13-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:09:13-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.44MB, end 206.46MB, increase: 14.11KB
2025-8-13 21:09:13-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:09:13-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:09:13-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:09:13-debug: [Build Memory track]: 填充脚本数据到 settings.json start:206.48MB, end 206.51MB, increase: 22.69KB
2025-8-13 21:09:13-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-8-13 21:09:13-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.53MB, end 206.65MB, increase: 126.29KB
2025-8-13 21:09:29-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:09:29-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (4ms)
2025-8-13 21:09:29-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:09:29-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (2ms)
2025-8-13 21:09:29-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:09:29-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (3ms)
2025-8-13 21:11:44-debug: refresh db internal success
2025-8-13 21:11:44-debug: refresh db assets success
2025-8-13 21:11:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:11:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:11:44-debug: asset-db:refresh-all-database (39ms)
2025-8-13 21:11:44-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 21:11:44-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:11:46-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/weapons/bullet.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:11:46-debug: asset-db:reimport-assetffdc0091-02b5-47e9-9cba-752ad0f7f260 (3ms)
2025-8-13 21:11:55-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:11:55-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (8ms)
2025-8-13 21:11:56-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:11:56-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (3ms)
2025-8-13 21:11:58-debug: Query all assets info in project
2025-8-13 21:11:58-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:11:58-debug: Skip compress image, progress: 0%
2025-8-13 21:11:58-debug: Init all bundles start..., progress: 0%
2025-8-13 21:11:58-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:11:58-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:11:58-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:11:58-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:11:58-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:11:58-debug:   Number of other assets: 579
2025-8-13 21:11:58-debug:   Number of all scripts: 28
2025-8-13 21:11:58-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:11:58-debug:   Number of all scenes: 3
2025-8-13 21:11:58-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-8-13 21:11:58-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 5%
2025-8-13 21:11:58-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:11:58-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:11:58-debug: [Build Memory track]: 查询 Asset Bundle start:208.12MB, end 208.15MB, increase: 29.57KB
2025-8-13 21:11:58-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-8-13 21:11:58-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-8-13 21:11:58-debug: [Build Memory track]: 查询 Asset Bundle start:208.17MB, end 208.29MB, increase: 121.20KB
2025-8-13 21:11:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:11:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:11:58-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-8-13 21:11:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.31MB, end 208.33MB, increase: 16.22KB
2025-8-13 21:11:58-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:11:58-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:11:58-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:11:58-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.35MB, end 208.37MB, increase: 15.31KB
2025-8-13 21:11:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:11:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:11:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 21:11:58-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-8-13 21:11:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.39MB, end 207.82MB, increase: -585.24KB
2025-8-13 21:14:01-debug: refresh db internal success
2025-8-13 21:14:01-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:01-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:01-debug: refresh db assets success
2025-8-13 21:14:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:14:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:14:01-debug: asset-db:refresh-all-database (42ms)
2025-8-13 21:14:01-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 21:14:01-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:14:01-debug: Query all assets info in project
2025-8-13 21:14:01-debug: init custom config: keepNodeUuid: false, useCache: true
2025-8-13 21:14:01-debug: Skip compress image, progress: 0%
2025-8-13 21:14:01-debug: Init all bundles start..., progress: 0%
2025-8-13 21:14:01-debug: Num of bundles: 3..., progress: 0%
2025-8-13 21:14:01-debug: 查询 Asset Bundle start, progress: 0%
2025-8-13 21:14:01-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:14:01-debug: Init bundle root assets start..., progress: 0%
2025-8-13 21:14:01-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-8-13 21:14:02-debug:   Number of all scenes: 3
2025-8-13 21:14:02-debug:   Number of all scripts: 28
2025-8-13 21:14:02-debug:   Number of other assets: 579
2025-8-13 21:14:02-debug: Init bundle root assets success..., progress: 0%
2025-8-13 21:14:02-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-8-13 21:14:02-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 5%
2025-8-13 21:14:02-debug: [Build Memory track]: 查询 Asset Bundle start:209.48MB, end 209.45MB, increase: -33.31KB
2025-8-13 21:14:02-debug: 查询 Asset Bundle start, progress: 5%
2025-8-13 21:14:02-debug: // ---- build task 查询 Asset Bundle ----
2025-8-13 21:14:02-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-8-13 21:14:02-debug: [Build Memory track]: 查询 Asset Bundle start:209.47MB, end 209.58MB, increase: 110.88KB
2025-8-13 21:14:02-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-8-13 21:14:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:14:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-8-13 21:14:02-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-8-13 21:14:02-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.60MB, end 209.63MB, increase: 23.65KB
2025-8-13 21:14:02-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-8-13 21:14:02-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-8-13 21:14:02-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-8-13 21:14:02-debug: [Build Memory track]: 填充脚本数据到 settings.json start:209.65MB, end 209.67MB, increase: 15.84KB
2025-8-13 21:14:02-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-8-13 21:14:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-8-13 21:14:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-8-13 21:14:02-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-8-13 21:14:02-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:209.69MB, end 209.82MB, increase: 134.63KB
2025-8-13 21:14:24-debug: refresh db internal success
2025-8-13 21:14:24-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:24-debug: refresh db assets success
2025-8-13 21:14:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:14:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:14:24-debug: asset-db:refresh-all-database (33ms)
2025-8-13 21:14:24-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:14:25-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:25-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (3ms)
2025-8-13 21:14:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:26-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (4ms)
2025-8-13 21:14:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:26-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (4ms)
2025-8-13 21:14:26-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:26-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (3ms)
2025-8-13 21:14:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:27-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (3ms)
2025-8-13 21:14:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:27-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (4ms)
2025-8-13 21:14:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:27-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (3ms)
2025-8-13 21:14:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:27-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (6ms)
2025-8-13 21:14:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:27-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (3ms)
2025-8-13 21:14:27-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:28-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (2ms)
2025-8-13 21:14:28-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:28-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (4ms)
2025-8-13 21:14:28-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:14:28-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (2ms)
2025-8-13 21:14:47-debug: refresh db internal success
2025-8-13 21:14:47-debug: refresh db assets success
2025-8-13 21:14:47-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:14:47-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:14:47-debug: asset-db:refresh-all-database (33ms)
2025-8-13 21:14:47-debug: asset-db:worker-effect-data-processing (1ms)
2025-8-13 21:14:47-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:15:13-debug: refresh db internal success
2025-8-13 21:15:13-debug: refresh db assets success
2025-8-13 21:15:13-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:15:13-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:15:13-debug: asset-db:refresh-all-database (36ms)
2025-8-13 21:15:13-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:16:36-debug: refresh db internal success
2025-8-13 21:16:36-debug: refresh db assets success
2025-8-13 21:16:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-8-13 21:16:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-8-13 21:16:36-debug: asset-db:refresh-all-database (38ms)
2025-8-13 21:16:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-8-13 21:16:37-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/resources/prefab/levels/level-1.prefab
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:16:37-debug: asset-db:reimport-asset5e122843-ecf2-42e7-ab74-ec19f70240a3 (4ms)
2025-8-13 21:16:41-debug: %cImport%c: /Users/<USER>/projects/cocos_project/driftClash/assets/Scenes/LevelSelect.scene
background: #aaff85; color: #000;
color: #000;
2025-8-13 21:16:41-debug: asset-db:reimport-asset091c5c0e-b72a-4cad-ab68-635bc57ff236 (10ms)
