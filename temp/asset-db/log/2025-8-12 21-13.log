2025-8-12 21:13:17-debug: start **** info
2025-8-12 21:13:17-log: Cannot access game frame or container.
2025-8-12 21:13:17-debug: asset-db:require-engine-code (373ms)
2025-8-12 21:13:17-log: meshopt wasm decoder initialized
2025-8-12 21:13:17-log: [box2d]:box2d wasm lib loaded.
2025-8-12 21:13:17-log: [bullet]:bullet wasm lib loaded.
2025-8-12 21:13:17-log: Cocos Creator v3.8.6
2025-8-12 21:13:17-log: Forward render pipeline initialized.
2025-8-12 21:13:18-debug: [Assets Memory track]: asset-db-plugin-register: builder start:88.63MB, end 224.63MB, increase: 136.00MB
2025-8-12 21:13:18-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:224.83MB, end 227.73MB, increase: 2.89MB
2025-8-12 21:13:18-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.77MB, end 227.89MB, increase: 147.12MB
2025-8-12 21:13:18-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.09MB, end 228.35MB, increase: 148.25MB
2025-8-12 21:13:17-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.90MB, end 88.60MB, increase: 7.71MB
2025-8-12 21:13:17-log: Using legacy pipeline
2025-8-12 21:13:17-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:29.43MB, end 80.08MB, increase: 50.65MB
2025-8-12 21:13:18-debug: run package(linux) handler(enable) start
2025-8-12 21:13:18-debug: run package(migu-mini-game) handler(enable) start
2025-8-12 21:13:18-debug: run package(migu-mini-game) handler(enable) success!
2025-8-12 21:13:18-debug: run package(native) handler(enable) start
2025-8-12 21:13:18-debug: run package(mac) handler(enable) start
2025-8-12 21:13:18-debug: run package(ohos) handler(enable) start
2025-8-12 21:13:18-debug: run package(ohos) handler(enable) success!
2025-8-12 21:13:18-debug: run package(oppo-mini-game) handler(enable) start
2025-8-12 21:13:18-debug: run package(native) handler(enable) success!
2025-8-12 21:13:18-debug: run package(linux) handler(enable) success!
2025-8-12 21:13:18-debug: run package(runtime-dev-tools) handler(enable) success!
2025-8-12 21:13:18-debug: run package(taobao-mini-game) handler(enable) start
2025-8-12 21:13:18-debug: run package(taobao-mini-game) handler(enable) success!
2025-8-12 21:13:18-debug: run package(vivo-mini-game) handler(enable) start
2025-8-12 21:13:18-debug: run package(vivo-mini-game) handler(enable) success!
2025-8-12 21:13:18-debug: run package(web-desktop) handler(enable) start
2025-8-12 21:13:18-debug: run package(web-desktop) handler(enable) success!
2025-8-12 21:13:18-debug: run package(web-mobile) handler(enable) start
2025-8-12 21:13:18-debug: run package(web-mobile) handler(enable) success!
2025-8-12 21:13:18-debug: run package(wechatgame) handler(enable) start
2025-8-12 21:13:18-debug: run package(wechatgame) handler(enable) success!
2025-8-12 21:13:18-debug: run package(mac) handler(enable) success!
2025-8-12 21:13:18-debug: run package(wechatprogram) handler(enable) success!
2025-8-12 21:13:18-debug: run package(windows) handler(enable) start
2025-8-12 21:13:18-debug: run package(wechatprogram) handler(enable) start
2025-8-12 21:13:18-debug: run package(xiaomi-quick-game) handler(enable) start
2025-8-12 21:13:18-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-8-12 21:13:18-debug: run package(oppo-mini-game) handler(enable) success!
2025-8-12 21:13:18-debug: run package(cocos-service) handler(enable) start
2025-8-12 21:13:18-debug: run package(windows) handler(enable) success!
2025-8-12 21:13:18-debug: run package(runtime-dev-tools) handler(enable) start
2025-8-12 21:13:18-debug: run package(im-plugin) handler(enable) start
2025-8-12 21:13:18-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-8-12 21:13:18-debug: run package(cocos-service) handler(enable) success!
2025-8-12 21:13:18-debug: run package(placeholder) handler(enable) start
2025-8-12 21:13:18-debug: run package(placeholder) handler(enable) success!
2025-8-12 21:13:18-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-8-12 21:13:18-debug: run package(im-plugin) handler(enable) success!
2025-8-12 21:13:18-debug: asset-db:worker-init: initPlugin (751ms)
2025-8-12 21:13:18-debug: [Assets Memory track]: asset-db:worker-init start:29.42MB, end 229.18MB, increase: 199.75MB
2025-8-12 21:13:18-debug: Run asset db hook programming:beforePreStart ...
2025-8-12 21:13:18-debug: Run asset db hook programming:beforePreStart success!
2025-8-12 21:13:18-debug: Run asset db hook engine-extends:beforePreStart ...
2025-8-12 21:13:18-debug: Run asset db hook engine-extends:beforePreStart success!
2025-8-12 21:13:18-debug: asset-db:worker-init (1201ms)
2025-8-12 21:13:18-debug: asset-db-hook-programming-beforePreStart (37ms)
2025-8-12 21:13:18-debug: asset-db-hook-engine-extends-beforePreStart (37ms)
2025-8-12 21:13:18-debug: Preimport db internal success
2025-8-12 21:13:18-debug: Preimport db assets success
2025-8-12 21:13:18-debug: Run asset db hook programming:afterPreStart ...
2025-8-12 21:13:18-debug: starting packer-driver...
2025-8-12 21:13:18-debug: initialize scripting environment...
2025-8-12 21:13:18-debug: [[Executor]] prepare before lock
2025-8-12 21:13:18-debug: Set detail map pack:///resolution-detail-map.json: {}
2025-8-12 21:13:18-debug: [[Executor]] prepare after unlock
2025-8-12 21:13:18-debug: Run asset db hook programming:afterPreStart success!
2025-8-12 21:13:18-debug: Run asset db hook engine-extends:afterPreStart ...
2025-8-12 21:13:18-debug: Run asset db hook engine-extends:afterPreStart success!
2025-8-12 21:13:18-debug: Start up the 'internal' database...
2025-8-12 21:13:18-debug: asset-db-hook-programming-afterPreStart (258ms)
2025-8-12 21:13:18-debug: asset-db-hook-engine-extends-afterPreStart (151ms)
2025-8-12 21:13:18-debug: asset-db:worker-effect-data-processing (151ms)
2025-8-12 21:13:18-debug: [Assets Memory track]: asset-db:worker-startup-database[internal] start:229.27MB, end 243.18MB, increase: 13.92MB
2025-8-12 21:13:18-debug: Start up the 'assets' database...
2025-8-12 21:13:18-debug: asset-db:worker-startup-database[internal] (397ms)
2025-8-12 21:13:19-debug: [Assets Memory track]: asset-db:worker-startup-database[assets] start:231.57MB, end 241.48MB, increase: 9.91MB
2025-8-12 21:13:19-debug: lazy register asset handler text
2025-8-12 21:13:19-debug: lazy register asset handler json
2025-8-12 21:13:19-debug: lazy register asset handler spine-data
2025-8-12 21:13:19-debug: [Assets Memory track]: asset-db:worker-init: startup start:228.57MB, end 241.49MB, increase: 12.92MB
2025-8-12 21:13:19-debug: lazy register asset handler dragonbones-atlas
2025-8-12 21:13:19-debug: lazy register asset handler terrain
2025-8-12 21:13:19-debug: lazy register asset handler javascript
2025-8-12 21:13:19-debug: lazy register asset handler typescript
2025-8-12 21:13:19-debug: lazy register asset handler scene
2025-8-12 21:13:19-debug: lazy register asset handler prefab
2025-8-12 21:13:19-debug: lazy register asset handler directory
2025-8-12 21:13:19-debug: lazy register asset handler tiled-map
2025-8-12 21:13:19-debug: lazy register asset handler buffer
2025-8-12 21:13:19-debug: lazy register asset handler image
2025-8-12 21:13:19-debug: lazy register asset handler sign-image
2025-8-12 21:13:19-debug: lazy register asset handler alpha-image
2025-8-12 21:13:19-debug: lazy register asset handler texture
2025-8-12 21:13:19-debug: lazy register asset handler texture-cube
2025-8-12 21:13:19-debug: lazy register asset handler erp-texture-cube
2025-8-12 21:13:19-debug: lazy register asset handler render-texture
2025-8-12 21:13:19-debug: lazy register asset handler sprite-frame
2025-8-12 21:13:19-debug: lazy register asset handler texture-cube-face
2025-8-12 21:13:19-debug: lazy register asset handler gltf
2025-8-12 21:13:19-debug: lazy register asset handler gltf-mesh
2025-8-12 21:13:19-debug: lazy register asset handler gltf-skeleton
2025-8-12 21:13:19-debug: lazy register asset handler gltf-animation
2025-8-12 21:13:19-debug: lazy register asset handler gltf-material
2025-8-12 21:13:19-debug: lazy register asset handler gltf-scene
2025-8-12 21:13:19-debug: lazy register asset handler gltf-embeded-image
2025-8-12 21:13:19-debug: lazy register asset handler fbx
2025-8-12 21:13:19-debug: lazy register asset handler material
2025-8-12 21:13:19-debug: lazy register asset handler physics-material
2025-8-12 21:13:19-debug: lazy register asset handler effect
2025-8-12 21:13:19-debug: lazy register asset handler effect-header
2025-8-12 21:13:19-debug: lazy register asset handler audio-clip
2025-8-12 21:13:19-debug: lazy register asset handler animation-clip
2025-8-12 21:13:19-debug: lazy register asset handler animation-graph
2025-8-12 21:13:19-debug: lazy register asset handler animation-graph-variant
2025-8-12 21:13:19-debug: lazy register asset handler animation-mask
2025-8-12 21:13:19-debug: lazy register asset handler ttf-font
2025-8-12 21:13:19-debug: lazy register asset handler bitmap-font
2025-8-12 21:13:19-debug: lazy register asset handler *
2025-8-12 21:13:19-debug: lazy register asset handler rt-sprite-frame
2025-8-12 21:13:19-debug: lazy register asset handler auto-atlas
2025-8-12 21:13:19-debug: lazy register asset handler label-atlas
2025-8-12 21:13:19-debug: lazy register asset handler render-pipeline
2025-8-12 21:13:19-debug: lazy register asset handler render-stage
2025-8-12 21:13:19-debug: lazy register asset handler render-flow
2025-8-12 21:13:19-debug: lazy register asset handler instantiation-material
2025-8-12 21:13:19-debug: lazy register asset handler instantiation-mesh
2025-8-12 21:13:19-debug: lazy register asset handler instantiation-skeleton
2025-8-12 21:13:19-debug: lazy register asset handler instantiation-animation
2025-8-12 21:13:19-debug: lazy register asset handler video-clip
2025-8-12 21:13:19-debug: lazy register asset handler dragonbones
2025-8-12 21:13:19-debug: lazy register asset handler sprite-atlas
2025-8-12 21:13:19-debug: lazy register asset handler particle
2025-8-12 21:13:19-debug: asset-db:start-database (430ms)
2025-8-12 21:13:19-debug: asset-db:ready (2877ms)
2025-8-12 21:13:19-debug: asset-db:worker-startup-database[assets] (372ms)
2025-8-12 21:13:19-debug: fix the bug of updateDefaultUserData
2025-8-12 21:13:19-debug: init worker message success
2025-8-12 21:13:19-debug: programming:execute-script (2ms)
2025-8-12 21:13:19-debug: [Build Memory track]: builder:worker-init start:244.58MB, end 256.45MB, increase: 11.87MB
2025-8-12 21:13:19-debug: builder:worker-init (197ms)
