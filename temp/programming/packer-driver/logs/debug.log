21:16:54.751 debug: 2025/8/13 21:16:54
21:16:54.751 debug: Project: /Users/<USER>/projects/cocos_project/driftClash
21:16:54.751 debug: Targets: editor,preview
21:16:54.752 debug: Incremental file seems great.
21:16:54.752 debug: Engine path: /Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine
21:16:54.757 debug: Initializing target [Editor]
21:16:54.757 debug: Loading cache
21:16:54.758 debug: Loading cache costs 1.3383329999996931ms.
21:16:54.759 debug: Engine features shipped in editor: base,gfx-webgl,gfx-webgl2,gfx-empty,gfx-webgpu,3d,animation,skeletal-animation,2d,rich-text,mask,graphics,ui-skew,ui,affine-transform,particle,particle-2d,physics-framework,physics-cannon,physics-physx,physics-ammo,physics-builtin,physics-2d-framework,physics-2d-box2d-jsb,physics-2d-box2d,physics-2d-builtin,physics-2d-box2d-wasm,intersection-2d,primitive,profiler,occlusion-query,geometry-renderer,debug-renderer,audio,video,xr,light-probe,terrain,webview,tween,tiled-map,vendor-google,spine-3.8,spine-4.2,dragon-bones,marionette,procedural-animation,custom-pipeline,custom-pipeline-builtin-scripts,custom-pipeline-post-process,legacy-pipeline,websocket,websocket-server,meshopt
21:16:54.759 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
21:16:54.759 debug: Initializing target [Preview]
21:16:54.759 debug: Loading cache
21:16:54.760 debug: Loading cache costs 1.0828329999999369ms.
21:16:54.760 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc"
  }
}
21:16:54.779 debug: Sync engine features: 2d,affine-transform,animation,audio,base,custom-pipeline,dragon-bones,gfx-webgl,gfx-webgl2,graphics,intersection-2d,mask,particle-2d,physics-2d-box2d,profiler,rich-text,spine-3.8,tiled-map,tween,ui,video,webview,custom-pipeline
21:16:54.781 debug: Reset databases. Enumerated domains: [
  {
    "root": "db://internal/",
    "physical": "/Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets"
  },
  {
    "root": "db://assets/",
    "physical": "/Users/<USER>/projects/cocos_project/driftClash/assets"
  }
]
21:16:54.781 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/"
  }
}
21:16:54.781 debug: Our import map(foo:/bar): {
  "imports": {
    "cc": "cce:/internal/x/cc",
    "db://internal/": "file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/",
    "db://assets/": "file:///Users/<USER>/projects/cocos_project/driftClash/assets/"
  }
}
21:16:54.781 debug: Pulling asset-db.
21:16:54.785 debug: Fetch asset-db cost: 3.5771249999997963ms.
21:16:54.785 debug: Build iteration starts.
Number of accumulated asset changes: 28
Feature changed: false
21:16:54.785 debug: Target(editor) build started.
21:16:54.786 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
21:16:54.786 debug: Inspect cce:/internal/x/cc
21:16:54.813 debug: transform url: 'cce:/internal/x/cc' costs: 27.00 ms
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/gfx-empty from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-empty.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/gfx-webgpu from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgpu.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/3d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/3d.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/skeletal-animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/skeletal-animation.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/ui-skew from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui-skew.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/particle from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-framework.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-cannon from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-cannon.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-physx from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-physx.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-ammo from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-ammo.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-builtin.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-jsb from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-jsb.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-2d-builtin from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-builtin.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d-wasm from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d-wasm.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/primitive from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/primitive.
21:16:54.814 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/geometry-renderer from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/geometry-renderer.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/xr from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/xr.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/light-probe from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/light-probe.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/terrain from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/terrain.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/vendor-google from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/vendor-google.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline-post-process from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline-post-process.
21:16:54.815 debug: Resolve cce:/internal/x/cc-fu/legacy-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/legacy-pipeline.
21:16:54.815 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Fri Jan 02 1970 06:00:10 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
21:16:54.815 debug: Inspect cce:/internal/x/prerequisite-imports
21:16:54.821 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 6.40 ms
21:16:54.822 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
21:16:54.822 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
21:16:54.822 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
21:16:54.822 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
21:16:54.822 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
21:16:54.822 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
21:16:54.822 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
21:16:54.823 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:54.823 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.823 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Tue Aug 12 2025 23:14:14 GMT+0800 (中国标准时间), Current mtime: Wed Aug 13 2025 21:16:51 GMT+0800 (中国标准时间)
21:16:54.823 debug: Inspect cce:/internal/code-quality/cr.mjs
21:16:54.826 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 3.00 ms
21:16:54.826 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
21:16:54.826 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
21:16:54.827 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
21:16:54.827 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
21:16:54.827 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
21:16:54.827 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
21:16:54.827 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
21:16:54.827 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
21:16:54.827 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
21:16:54.827 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.827 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.827 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.827 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
21:16:54.827 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
21:16:54.827 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
21:16:54.827 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:54.828 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
21:16:54.828 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:54.828 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:54.828 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:54.828 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.828 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts.
21:16:54.828 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
21:16:54.828 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts.
21:16:54.828 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:54.828 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.828 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.828 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts.
21:16:54.828 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:54.828 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.828 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
21:16:54.828 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
21:16:54.828 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:54.830 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:54.830 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.830 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.830 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
21:16:54.830 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:54.831 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.831 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
21:16:54.831 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
21:16:54.831 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:54.831 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:54.831 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts.
21:16:54.831 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts.
21:16:54.835 debug: Target(editor) ends with cost 49.86887499999966ms.
21:16:54.835 debug: Target(preview) build started.
21:16:54.836 debug: Detected change: cce:/internal/x/cc. Last mtime: Thu Jan 01 1970 08:00:02 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
21:16:54.836 debug: Inspect cce:/internal/x/cc
21:16:54.846 debug: transform url: 'cce:/internal/x/cc' costs: 9.70 ms
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/2d.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/sorting from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/sorting.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/affine-transform from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/affine-transform.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/animation from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/animation.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/audio from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/audio.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/base from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/base.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/custom-pipeline from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/custom-pipeline.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/dragon-bones from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/dragon-bones.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/gfx-webgl2 from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/gfx-webgl2.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/graphics from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/graphics.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/intersection-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/intersection-2d.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/mask from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/mask.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/particle-2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/particle-2d.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/physics-2d-box2d from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-box2d.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/physics-2d-framework from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/physics-2d-framework.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/profiler from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/profiler.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/rich-text from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/rich-text.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/spine from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/spine.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/tiled-map from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tiled-map.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/tween from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/tween.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/ui from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/ui.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/video from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/video.
21:16:54.847 debug: Resolve cce:/internal/x/cc-fu/webview from cce:/internal/x/cc as external dependency cce:/internal/x/cc-fu/webview.
21:16:54.847 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Fri Jan 02 1970 06:00:10 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
21:16:54.847 debug: Inspect cce:/internal/x/prerequisite-imports
21:16:54.851 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 4.30 ms
21:16:54.852 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
21:16:54.852 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
21:16:54.852 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
21:16:54.852 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
21:16:54.852 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
21:16:54.852 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.852 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
21:16:54.853 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
21:16:54.853 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:54.853 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.853 debug: Detected change: cce:/internal/code-quality/cr.mjs. Last mtime: Tue Aug 12 2025 23:14:14 GMT+0800 (中国标准时间), Current mtime: Wed Aug 13 2025 21:16:51 GMT+0800 (中国标准时间)
21:16:54.853 debug: Inspect cce:/internal/code-quality/cr.mjs
21:16:54.856 debug: transform url: 'cce:/internal/code-quality/cr.mjs' costs: 3.90 ms
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as external dependency cc/env.
21:16:54.857 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
21:16:54.857 debug: Resolve ./builtin-pipeline-pass from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
21:16:54.857 debug: Resolve ./builtin-pipeline from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
21:16:54.857 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as external dependency cc/env.
21:16:54.857 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve ./builtin-pipeline-settings from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
21:16:54.857 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts as external dependency cc/env.
21:16:54.857 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc/env from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as external dependency cc/env.
21:16:54.857 debug: Resolve ./builtin-pipeline-types from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.857 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.857 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.857 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as cce:/internal/x/cc.
21:16:54.857 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
21:16:54.857 debug: Resolve ./camera_follow from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
21:16:54.857 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:54.857 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
21:16:54.857 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:54.858 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:54.858 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:54.858 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.858 debug: Resolve ./PaintManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts.
21:16:54.858 debug: Resolve ./GameOverPanel from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
21:16:54.858 debug: Resolve ./GameHUD from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts.
21:16:54.858 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:54.858 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.858 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.858 debug: Resolve ./Bullet from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts.
21:16:54.858 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:54.858 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.858 debug: Resolve ./AIController from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
21:16:54.858 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve ./player from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:54.858 debug: Resolve ./AIPlayer from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:54.858 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.858 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
21:16:54.858 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.859 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve ./CarProperties from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:54.859 debug: Resolve ./SoundManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve ./GameManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:54.859 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cce:/internal/code-quality/cr.mjs from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/code-quality/cr.mjs.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve cc from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as cce:/internal/x/cc.
21:16:54.859 debug: Resolve ./TempData from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
21:16:54.859 debug: Resolve ./PlayerManager from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:54.859 debug: Resolve ./SceneTransition from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:54.859 debug: Resolve ./CarPropertyDisplay from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts.
21:16:54.859 debug: Resolve ./PurchasePanel from file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts.
21:16:54.863 debug: Target(preview) ends with cost 27.3681660000002ms.
21:16:55.108 debug: Pulling asset-db.
21:16:55.342 debug: Fetch asset-db cost: 233.34749999999985ms.
21:16:55.343 debug: Build iteration starts.
Number of accumulated asset changes: 28
Feature changed: false
21:16:55.343 debug: Target(editor) build started.
21:16:55.344 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
21:16:55.344 debug: Inspect cce:/internal/x/prerequisite-imports
21:16:55.348 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.90 ms
21:16:55.348 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
21:16:55.349 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
21:16:55.349 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
21:16:55.349 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
21:16:55.349 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
21:16:55.349 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts.
21:16:55.349 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
21:16:55.350 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
21:16:55.350 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:55.350 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts.
21:16:55.350 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:55.350 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
21:16:55.350 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:55.350 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
21:16:55.350 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
21:16:55.350 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:55.353 debug: Target(editor) ends with cost 10.163708000000042ms.
21:16:55.354 debug: Target(preview) build started.
21:16:55.354 debug: Detected change: cce:/internal/x/prerequisite-imports. Last mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间), Current mtime: Thu Jan 01 1970 08:00:03 GMT+0800 (中国标准时间)
21:16:55.355 debug: Inspect cce:/internal/x/prerequisite-imports
21:16:55.358 debug: transform url: 'cce:/internal/x/prerequisite-imports' costs: 3.30 ms
21:16:55.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts.
21:16:55.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts.
21:16:55.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts.
21:16:55.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts.
21:16:55.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts.
21:16:55.358 debug: Resolve file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts from cce:/internal/x/prerequisite-imports as file:///Applications/Cocos/Creator/3.8.6/CocosCreator.app/Contents/Resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIController.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/AIPlayer.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarProperties.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/CarPropertyDisplay.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameHUD.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameManager.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/GameOverPanel.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/HealthBarUI.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/MainMenuController.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintManager.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintSpot.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PausePanel.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerInfoUI.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PlayerManager.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PurchasePanel.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SceneTransition.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SoundManager.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/TempData.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/camera_follow.ts.
21:16:55.359 debug: Resolve file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts from cce:/internal/x/prerequisite-imports as file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/player.ts.
21:16:55.364 debug: Target(preview) ends with cost 10.230166000000281ms.
