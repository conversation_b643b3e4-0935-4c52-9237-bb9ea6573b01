{"version": 3, "sources": ["file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/PaintCoordinateTest.ts"], "names": ["_decorator", "Component", "Label", "UITransform", "view", "Vec3", "find", "ccclass", "property", "PaintCoordinateTest", "canvas", "canvasTransform", "onLoad", "node", "scene", "getChildByName", "getComponent", "start", "debugLabel", "updateDebugInfo", "schedule", "visibleSize", "getVisibleSize", "designResolution", "getDesignResolutionSize", "canvasSize", "contentSize", "canvasScale", "scale", "scaleX", "width", "scaleY", "height", "debugInfo", "toFixed", "x", "y", "join", "string", "testCoordinateConversion", "worldX", "worldY", "console", "warn", "worldPos", "localPos", "convertToNodeSpaceAR", "log", "validatePaintPositions", "paintManagerNode"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,W,OAAAA,W;AAAqBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;;;;;;;;OACxE;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;AAE9B;AACA;AACA;AACA;;qCAEaS,mB,WADZF,OAAO,CAAC,qBAAD,C,UAEHC,QAAQ,CAACN,KAAD,C,2BAFb,MACaO,mBADb,SACyCR,SADzC,CACmD;AAAA;AAAA;;AAAA;;AAAA,eAIvCS,MAJuC,GAIxB,IAJwB;AAAA,eAKvCC,eALuC,GAKR,IALQ;AAAA;;AAO/CC,QAAAA,MAAM,GAAG;AAAA;;AACL;AACA,eAAKF,MAAL,uBAAc,KAAKG,IAAL,CAAUC,KAAxB,qBAAc,iBAAiBC,cAAjB,CAAgC,QAAhC,CAAd;;AACA,cAAI,KAAKL,MAAT,EAAiB;AACb,iBAAKC,eAAL,GAAuB,KAAKD,MAAL,CAAYM,YAAZ,CAAyBb,WAAzB,CAAvB;AACH;AACJ;;AAEDc,QAAAA,KAAK,GAAG;AACJ,cAAI,KAAKC,UAAT,EAAqB;AACjB,iBAAKC,eAAL,GADiB,CAEjB;;AACA,iBAAKC,QAAL,CAAc,KAAKD,eAAnB,EAAoC,GAApC;AACH;AACJ;AAED;AACJ;AACA;;;AACYA,QAAAA,eAAe,GAAS;AAC5B,cAAI,CAAC,KAAKD,UAAN,IAAoB,CAAC,KAAKR,MAA1B,IAAoC,CAAC,KAAKC,eAA9C,EAA+D;AAC3D;AACH,WAH2B,CAK5B;;;AACA,cAAMU,WAAW,GAAGjB,IAAI,CAACkB,cAAL,EAApB;AACA,cAAMC,gBAAgB,GAAGnB,IAAI,CAACoB,uBAAL,EAAzB,CAP4B,CAS5B;;AACA,cAAMC,UAAU,GAAG,KAAKd,eAAL,CAAqBe,WAAxC;AACA,cAAMC,WAAW,GAAG,KAAKjB,MAAL,CAAYkB,KAAhC,CAX4B,CAa5B;;AACA,cAAMC,MAAM,GAAGR,WAAW,CAACS,KAAZ,GAAoBP,gBAAgB,CAACO,KAApD;AACA,cAAMC,MAAM,GAAGV,WAAW,CAACW,MAAZ,GAAqBT,gBAAgB,CAACS,MAArD;AAEA,cAAMC,SAAS,GAAG,gFAELZ,WAAW,CAACS,KAAZ,CAAkBI,OAAlB,CAA0B,CAA1B,CAFK,WAE6Bb,WAAW,CAACW,MAAZ,CAAmBE,OAAnB,CAA2B,CAA3B,CAF7B,uCAGJX,gBAAgB,CAACO,KAAjB,CAAuBI,OAAvB,CAA+B,CAA/B,CAHI,WAGmCX,gBAAgB,CAACS,MAAjB,CAAwBE,OAAxB,CAAgC,CAAhC,CAHnC,2BAIDT,UAAU,CAACK,KAAX,CAAiBI,OAAjB,CAAyB,CAAzB,CAJC,WAIgCT,UAAU,CAACO,MAAX,CAAkBE,OAAlB,CAA0B,CAA1B,CAJhC,2BAKDP,WAAW,CAACQ,CAAZ,CAAcD,OAAd,CAAsB,CAAtB,CALC,WAK6BP,WAAW,CAACS,CAAZ,CAAcF,OAAd,CAAsB,CAAtB,CAL7B,iCAMLL,MAAM,CAACK,OAAP,CAAe,CAAf,CANK,WAMkBH,MAAM,CAACG,OAAP,CAAe,CAAf,CANlB,wNAYhBG,IAZgB,CAYX,IAZW,CAAlB;AAcA,eAAKnB,UAAL,CAAgBoB,MAAhB,GAAyBL,SAAzB;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACWM,QAAAA,wBAAwB,CAACC,MAAD,EAAiBC,MAAjB,EAA2D;AACtF,cAAI,CAAC,KAAK9B,eAAV,EAA2B;AACvB+B,YAAAA,OAAO,CAACC,IAAR,CAAa,YAAb;AACA,mBAAO;AAAER,cAAAA,CAAC,EAAE,CAAL;AAAQC,cAAAA,CAAC,EAAE;AAAX,aAAP;AACH;;AAED,cAAMQ,QAAQ,GAAG,IAAIvC,IAAJ,CAASmC,MAAT,EAAiBC,MAAjB,EAAyB,CAAzB,CAAjB;AACA,cAAMI,QAAQ,GAAG,KAAKlC,eAAL,CAAqBmC,oBAArB,CAA0CF,QAA1C,CAAjB;AAEAF,UAAAA,OAAO,CAACK,GAAR,qEAA4BP,MAA5B,UAAuCC,MAAvC,sCAA0DI,QAAQ,CAACV,CAAT,CAAWD,OAAX,CAAmB,CAAnB,CAA1D,UAAoFW,QAAQ,CAACT,CAAT,CAAWF,OAAX,CAAmB,CAAnB,CAApF;AAEA,iBAAO;AAAEC,YAAAA,CAAC,EAAEU,QAAQ,CAACV,CAAd;AAAiBC,YAAAA,CAAC,EAAES,QAAQ,CAACT;AAA7B,WAAP;AACH;AAED;AACJ;AACA;AACA;;;AACWY,QAAAA,sBAAsB,GAAS;AAClC,cAAMC,gBAAgB,GAAG3C,IAAI,CAAC,cAAD,CAA7B;;AACA,cAAI,CAAC2C,gBAAL,EAAuB;AACnBP,YAAAA,OAAO,CAACC,IAAR,CAAa,mBAAb;AACA;AACH;;AAEDD,UAAAA,OAAO,CAACK,GAAR,CAAY,gBAAZ;AACAL,UAAAA,OAAO,CAACK,GAAR,CAAY,mBAAZ,EARkC,CAUlC;AACA;AACH;;AAhG8C,O;;;;;iBAE3B,I", "sourcesContent": ["import { _decorator, Component, Node, Label, UITransform, Canvas, view, Vec3, find } from 'cc';\nconst { ccclass, property } = _decorator;\n\n/**\n * 颜料坐标测试组件\n * 用于验证颜料坐标修复是否有效\n */\n@ccclass('PaintCoordinateTest')\nexport class PaintCoordinateTest extends Component {\n    @property(Label)\n    debugLabel: Label = null!;\n\n    private canvas: Node = null!;\n    private canvasTransform: UITransform = null!;\n\n    onLoad() {\n        // 获取Canvas节点\n        this.canvas = this.node.scene?.getChildByName('Canvas')!;\n        if (this.canvas) {\n            this.canvasTransform = this.canvas.getComponent(UITransform)!;\n        }\n    }\n\n    start() {\n        if (this.debugLabel) {\n            this.updateDebugInfo();\n            // 每秒更新一次调试信息\n            this.schedule(this.updateDebugInfo, 1.0);\n        }\n    }\n\n    /**\n     * 更新调试信息\n     */\n    private updateDebugInfo(): void {\n        if (!this.debugLabel || !this.canvas || !this.canvasTransform) {\n            return;\n        }\n\n        // 获取屏幕信息\n        const visibleSize = view.getVisibleSize();\n        const designResolution = view.getDesignResolutionSize();\n        \n        // 获取Canvas信息\n        const canvasSize = this.canvasTransform.contentSize;\n        const canvasScale = this.canvas.scale;\n\n        // 计算适配比例\n        const scaleX = visibleSize.width / designResolution.width;\n        const scaleY = visibleSize.height / designResolution.height;\n\n        const debugInfo = [\n            `=== 屏幕适配信息 ===`,\n            `可视区域: ${visibleSize.width.toFixed(0)} x ${visibleSize.height.toFixed(0)}`,\n            `设计分辨率: ${designResolution.width.toFixed(0)} x ${designResolution.height.toFixed(0)}`,\n            `Canvas尺寸: ${canvasSize.width.toFixed(0)} x ${canvasSize.height.toFixed(0)}`,\n            `Canvas缩放: ${canvasScale.x.toFixed(3)} x ${canvasScale.y.toFixed(3)}`,\n            `适配比例: ${scaleX.toFixed(3)} x ${scaleY.toFixed(3)}`,\n            ``,\n            `=== 坐标系统状态 ===`,\n            `颜料容器已修复: ✓`,\n            `坐标转换已修复: ✓`,\n            `爆炸清除已修复: ✓`\n        ].join('\\n');\n\n        this.debugLabel.string = debugInfo;\n    }\n\n    /**\n     * 测试坐标转换\n     * @param worldX 世界坐标X\n     * @param worldY 世界坐标Y\n     * @returns 本地坐标\n     */\n    public testCoordinateConversion(worldX: number, worldY: number): { x: number, y: number } {\n        if (!this.canvasTransform) {\n            console.warn('Canvas未初始化');\n            return { x: 0, y: 0 };\n        }\n\n        const worldPos = new Vec3(worldX, worldY, 0);\n        const localPos = this.canvasTransform.convertToNodeSpaceAR(worldPos);\n        \n        console.log(`坐标转换测试: 世界坐标(${worldX}, ${worldY}) -> 本地坐标(${localPos.x.toFixed(2)}, ${localPos.y.toFixed(2)})`);\n        \n        return { x: localPos.x, y: localPos.y };\n    }\n\n    /**\n     * 验证颜料位置是否正确\n     * 这个方法可以在游戏运行时调用来验证颜料位置\n     */\n    public validatePaintPositions(): void {\n        const paintManagerNode = find('PaintManager');\n        if (!paintManagerNode) {\n            console.warn('未找到PaintManager节点');\n            return;\n        }\n\n        console.log('=== 颜料位置验证 ===');\n        console.log('PaintManager节点已找到');\n\n        // 这里可以添加更多的验证逻辑\n        // 由于PaintManager是单例，可以通过getInstance获取\n    }\n}\n"]}