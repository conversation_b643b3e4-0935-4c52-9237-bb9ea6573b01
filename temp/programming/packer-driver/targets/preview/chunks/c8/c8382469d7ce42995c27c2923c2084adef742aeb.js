System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts at runtime.
      throw new Error("SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/Bullet.ts: Unexpected reserved word 'private'. (132:4)\n\n  130 |      * \u78B0\u649E\u56DE\u8C03\n  131 |      */\n> 132 |     private onBeginContact(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact) {\n      |     ^\n  133 |         const otherNode = otherCollider.node;\n  134 |         \n  135 |         // \u4E0D\u4E0E\u53D1\u5C04\u8005\u78B0\u649E");
    }
  };
});
//# sourceMappingURL=c8382469d7ce42995c27c2923c2084adef742aeb.js.map