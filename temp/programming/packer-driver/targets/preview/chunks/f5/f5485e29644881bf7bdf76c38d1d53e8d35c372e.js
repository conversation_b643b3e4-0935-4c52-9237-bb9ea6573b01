System.register([], function (_export, _context) {
  "use strict";

  return {
    setters: [],
    execute: function () {
      // This module is auto-generated to report error emitted when try to load module file:///Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts at runtime.
      throw new Error("SyntaxError: /file:/Users/<USER>/projects/cocos_project/driftClash/assets/scripts/SelectManager.ts: Unterminated string constant. (63:17)\n\n  61 |     // \u8F66\u8F86\u4EF7\u683C\u914D\u7F6E\n  62 |     private carInfos: CarinfoConfig = {\n> 63 |         'car-1': \"\u64CD\u63A7\u6027\u8D85\u5F3A\u5C0F\u8F66\uFF0C\u6B66\u5668\u914D\u5907\u4E3A\u5B50\u5F39\u53D1\u5C04\u5668\uFF0C\u51FB\u4E2D\u5BF9\u624B\u4F60\u53EF\u9020\u6210\u4F24\u5BB3This super maneuverable car is equipped with a bullet launcher. When you hit your opponent, you can cause damage.',      \n     |                  ^\n  64 |         'car-2': '\u7ECF\u5178\u8DD1\u8F66,\u5177\u6709\u575A\u56FA\u7684\u8F66\u8EAB,\u6B66\u5668\u914D\u5907\u4E3A\u706B\u7BAD\u70AE\uFF0C\u7206\u70B8\u540E\u4F1A\u6E05\u9664\u9644\u8FD1\u7684\u989C\u6599',    \n  65 |         'car-3': '\u73B0\u4EE3\u5316\u7684\u8D85\u7EA7\u8DD1\u8F66\uFF0C\u901F\u5EA6\u4E0E\u8F6C\u5411\u5747\u8861\uFF0C\u914D\u5907\u6B66\u5668\u4E3A\u673A\u70AE\uFF0C\u51FB\u4E2D\u540E\u53EF\u9020\u6210\u4F24\u5BB3',   \n  66 |         'car-4': '\u7529\u5C3E\u52A0\u901F\u72B9\u5982\u95EA\u7535\uFF0C\u6B66\u5668\u914D\u5907\u4E3A\u706B\u7BAD\u70AE\uFF0C\u7206\u70B8\u540E\u4F1A\u6E05\u9664\u9644\u8FD1\u7684\u989C\u6599',   ");
    }
  };
});
//# sourceMappingURL=f5485e29644881bf7bdf76c38d1d53e8d35c372e.js.map