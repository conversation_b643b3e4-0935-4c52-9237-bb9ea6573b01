"[\n  {\n    \"__type__\": \"cc.SceneAsset\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_native\": \"\",\n    \"scene\": {\n      \"__id__\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.Scene\",\n    \"_name\": \"LevelSelect\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": null,\n    \"_children\": [\n      {\n        \"__id__\": 2\n      },\n      {\n        \"__id__\": 240\n      },\n      {\n        \"__id__\": 242\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": {\n      \"__id__\": 244\n    },\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"autoReleaseAssets\": true,\n    \"_globals\": {\n      \"__id__\": 245\n    },\n    \"_id\": \"091c5c0e-b72a-4cad-ab68-635bc57ff236\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Canvas\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 1\n    },\n    \"_children\": [\n      {\n        \"__id__\": 3\n      },\n      {\n        \"__id__\": 5\n      },\n      {\n        \"__id__\": 8\n      },\n      {\n        \"__id__\": 22\n      },\n      {\n        \"__id__\": 76\n      },\n      {\n        \"__id__\": 143\n      },\n      {\n        \"__id__\": 150\n      },\n      {\n        \"__id__\": 157\n      },\n      {\n        \"__id__\": 198\n      },\n      {\n        \"__id__\": 219\n      },\n      {\n        \"__id__\": 222\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 237\n      },\n      {\n        \"__id__\": 238\n      },\n      {\n        \"__id__\": 239\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 640,\n      \"y\": 360.00000000000006,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"beI88Z2HpFELqR4T5EMHpg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Camera\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 4\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 1000\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ebFwiq8gBFaYpqYbdoDODe\"\n  },\n  {\n    \"__type__\": \"cc.Camera\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 3\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_projection\": 0,\n    \"_priority\": 0,\n    \"_fov\": 45,\n    \"_fovAxis\": 0,\n    \"_orthoHeight\": 380.724946695096,\n    \"_near\": 0,\n    \"_far\": 2000,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_depth\": 1,\n    \"_stencil\": 0,\n    \"_clearFlags\": 7,\n    \"_rect\": {\n      \"__type__\": \"cc.Rect\",\n      \"x\": 0,\n      \"y\": 0,\n      \"width\": 1,\n      \"height\": 1\n    },\n    \"_aperture\": 19,\n    \"_shutter\": 7,\n    \"_iso\": 0,\n    \"_screenScale\": 1,\n    \"_visibility\": 1108344832,\n    \"_targetTexture\": null,\n    \"_postProcess\": null,\n    \"_usePostProcess\": false,\n    \"_cameraType\": -1,\n    \"_trackingType\": 0,\n    \"_id\": \"63WIch3o5BEYRlXzTT0oWc\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"background2\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 6\n      },\n      {\n        \"__id__\": 7\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -5.684341886080802e-14,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"87V7okQpdCfpYxbLerHV0e\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 1595.403,\n      \"height\": 897.4141875\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"bci4QaVZtD7J8OmwyJSj34\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"b2b39a66-2564-4be8-8802-d043d12ffd8b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"caknzO4K5GyrIakBPuD31g\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"PlayerInfo\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 9\n      },\n      {\n        \"__id__\": 12\n      },\n      {\n        \"__id__\": 15\n      },\n      {\n        \"__id__\": 18\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 21\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 300,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"689snO44xDSIO8SZMf9m/H\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"BioPic\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 8\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 10\n      },\n      {\n        \"__id__\": 11\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -560.688,\n      \"y\": -4.788999999999987,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c2sQMvCjpBB5gzZzVEW1Uq\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 9\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 73.494,\n      \"height\": 63.482\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d9+fvaPZpCe56Sc1HNexUD\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 9\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"a47a6013-b8ef-4f76-8a70-0a5d0f2ba81b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"4909klZfVIWZeysyVMSeqh\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"name\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 8\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 13\n      },\n      {\n        \"__id__\": 14\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -464.403,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"90V84GKN9PXYUFg8NmJG9b\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 12\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 95.994140625,\n      \"height\": 50.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"30dsGjni5FLYaTZW1cO1Ld\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 12\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 223,\n      \"g\": 0,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_string\": \"Driver\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 36,\n    \"_fontSize\": 36,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": false,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"cdAQSeLjpJo7xcBLkcpTmI\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"coinIcon\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 8\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 16\n      },\n      {\n        \"__id__\": 17\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 608.44,\n      \"y\": 12.801,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c9zPBIm0dBzKNdDAn7fnvl\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 15\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 50,\n      \"height\": 50\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": 0.5\n    },\n    \"_id\": \"dcdYb7vzdMPoYmP9emRea4\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 15\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"cff45180-1973-4a53-8d9e-791cf8f042d8@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"d6IgxRyjVA1r6vtnY3y2t+\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"moneytext\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 8\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 19\n      },\n      {\n        \"__id__\": 20\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 544.611,\n      \"y\": 10.241,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"24eGY26D9KlZs5UAzxwbcJ\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 18\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 84.0859375,\n      \"height\": 54.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 1,\n      \"y\": 0.5\n    },\n    \"_id\": \"40fYrhDp9D0r6xmbL0WgKF\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 18\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 235,\n      \"b\": 128,\n      \"a\": 247\n    },\n    \"_string\": \"9999\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 36,\n    \"_fontSize\": 36,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"9aGlfTyqlA75ZJs2AjwJeO\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 8\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 100\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"5b0aG9j1tMsIPLHrqblKvy\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"level-scroll\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 23\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 73\n      },\n      {\n        \"__id__\": 74\n      },\n      {\n        \"__id__\": 75\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 41.007,\n      \"y\": 118.547,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"daKBzPOMxGYZ+EYft6QKW0\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"view\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 22\n    },\n    \"_children\": [\n      {\n        \"__id__\": 24\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 70\n      },\n      {\n        \"__id__\": 71\n      },\n      {\n        \"__id__\": 72\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 15.49,\n      \"y\": -7.668,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"9bZsvDhopEcq3KvFIJ4LEB\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"levels\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 23\n    },\n    \"_children\": [\n      {\n        \"__id__\": 25\n      },\n      {\n        \"__id__\": 39\n      },\n      {\n        \"__id__\": 53\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 67\n      },\n      {\n        \"__id__\": 68\n      },\n      {\n        \"__id__\": 69\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 21.899,\n      \"y\": 10.544,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"56zg3HWCRJta/BTO2QTmAu\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"level-1\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 24\n    },\n    \"_children\": [\n      {\n        \"__id__\": 26\n      },\n      {\n        \"__id__\": 29\n      },\n      {\n        \"__id__\": 32\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 35\n      },\n      {\n        \"__id__\": 36\n      },\n      {\n        \"__id__\": 37\n      },\n      {\n        \"__id__\": 38\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -348,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"09SZwFwsdKV43/+easV+Wc\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"mark\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 25\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 27\n      },\n      {\n        \"__id__\": 28\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -0.823,\n      \"y\": -13.402,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ecWgaIxGZOl6Y9P1fScCzH\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 26\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 109.195,\n      \"height\": 119.036\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"6b2Se+BPFA2KrvLPzW7Drw\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 26\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"4befdd25-e632-4e7e-aa34-************@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"44XJWHeo9A4qrhVSi72wvq\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"GradeLabel\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 25\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 30\n      },\n      {\n        \"__id__\": 31\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 129.724,\n      \"y\": -114.263,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"4eKHqmlhREj4qjmCBoAJZb\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 29\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 44,\n      \"height\": 54.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c909H0N6VINofZNhS/hGAB\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 29\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 169,\n      \"g\": 255,\n      \"b\": 59,\n      \"a\": 255\n    },\n    \"_string\": \"评价\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 20,\n    \"_fontSize\": 20,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"b6A98ZAFRJO4K57tKjbxp3\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"lock\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 25\n    },\n    \"_children\": [],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 33\n      },\n      {\n        \"__id__\": 34\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.5,\n      \"y\": 0.5,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"3bGbVpTVxKH6JCwNoQTwDZ\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 32\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 139,\n      \"height\": 186\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"7e4b77AfVElpvTBQtngg42\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 32\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"5c270278-c097-4dba-9f15-f3886683bff7@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"85tsUK+7VITojdDsXE8GcT\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 25\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 300,\n      \"height\": 200\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"a7/ZaULqpCx56qcssi0BKU\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 25\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"0bfa81f7-9b43-4f2a-bc44-2375066c8694@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"20cGQoY7dEdLS7Li6YtqQN\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 25\n    },\n    \"_enabled\": false,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 2,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": {\n      \"__uuid__\": \"0bfa81f7-9b43-4f2a-bc44-2375066c8694@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_hoverSprite\": {\n      \"__uuid__\": \"20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_pressedSprite\": {\n      \"__uuid__\": \"544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_disabledSprite\": {\n      \"__uuid__\": \"951249e0-9f16-456d-8b85-a6ca954da16b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 25\n    },\n    \"_id\": \"25gXHwXnNLI7ucJv83sdqj\"\n  },\n  {\n    \"__type__\": \"cc.Toggle\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 25\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 0,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 25\n    },\n    \"checkEvents\": [],\n    \"_isChecked\": true,\n    \"_checkMark\": {\n      \"__id__\": 28\n    },\n    \"_id\": \"18KGD5IIlDmoUNOUiA5NeR\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"level-2\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 24\n    },\n    \"_children\": [\n      {\n        \"__id__\": 40\n      },\n      {\n        \"__id__\": 43\n      },\n      {\n        \"__id__\": 46\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 49\n      },\n      {\n        \"__id__\": 50\n      },\n      {\n        \"__id__\": 51\n      },\n      {\n        \"__id__\": 52\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"a1IMFLV9xFv4XlbJa/SR5t\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"mark\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 39\n    },\n    \"_children\": [],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 41\n      },\n      {\n        \"__id__\": 42\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1.100999999999999,\n      \"y\": -15.36099999999999,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"3b0Op18UlI/7vyAMqmaY+i\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 40\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 109.195,\n      \"height\": 119.036\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"52T3Elpp5Cwb8m3dT96R4F\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 40\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"4befdd25-e632-4e7e-aa34-************@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"8281ZSnq9HSLTai4okheL0\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"GradeLabel\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 39\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 44\n      },\n      {\n        \"__id__\": 45\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 132.207,\n      \"y\": -114.219,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5f7lPI3tRCBqPmTtA5tIzG\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 43\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 44,\n      \"height\": 54.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"17pyPGW5ZH8KF2h1/YLw3m\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 43\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 138,\n      \"g\": 255,\n      \"b\": 109,\n      \"a\": 255\n    },\n    \"_string\": \"评价\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 20,\n    \"_fontSize\": 20,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"2fse4SH2lHuafLzIMuQ/Oh\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"lock\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 39\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 47\n      },\n      {\n        \"__id__\": 48\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.5,\n      \"y\": 0.5,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b2uUjnfMRNHb3HXYGInxky\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 46\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 139,\n      \"height\": 186\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c5HLyhuGRNuYz60gapCPHm\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 46\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"5c270278-c097-4dba-9f15-f3886683bff7@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"b0BhdnPt1KXrB9/q4GapP+\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 39\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 300,\n      \"height\": 200\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"e2E2/1nLBAJ5gqVatZLwxk\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 39\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"807a7b47-3819-4529-931c-683fbcf44eb0@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"22Ix10l+xNWamYMR5inNNb\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 39\n    },\n    \"_enabled\": false,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 2,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": {\n      \"__uuid__\": \"807a7b47-3819-4529-931c-683fbcf44eb0@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_hoverSprite\": {\n      \"__uuid__\": \"20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_pressedSprite\": {\n      \"__uuid__\": \"544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_disabledSprite\": {\n      \"__uuid__\": \"951249e0-9f16-456d-8b85-a6ca954da16b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 39\n    },\n    \"_id\": \"2eiuftLqNIWJqFG0Fg41my\"\n  },\n  {\n    \"__type__\": \"cc.Toggle\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 39\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 0,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 39\n    },\n    \"checkEvents\": [],\n    \"_isChecked\": false,\n    \"_checkMark\": {\n      \"__id__\": 42\n    },\n    \"_id\": \"475Zw5wuRC2LTg9f9mjEH8\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"level-3\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 24\n    },\n    \"_children\": [\n      {\n        \"__id__\": 54\n      },\n      {\n        \"__id__\": 57\n      },\n      {\n        \"__id__\": 60\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 63\n      },\n      {\n        \"__id__\": 64\n      },\n      {\n        \"__id__\": 65\n      },\n      {\n        \"__id__\": 66\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 348,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"717K2EVE1IzL8YYd+uKsmv\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"mark\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 53\n    },\n    \"_children\": [],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 55\n      },\n      {\n        \"__id__\": 56\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -4.25,\n      \"y\": -14.477,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"46jdJ6J1pBjp4Bb+r0V3r4\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 54\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 109.195,\n      \"height\": 119.036\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"99TH0M49xCs4xfKQjunH4B\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 54\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"4befdd25-e632-4e7e-aa34-************@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"8aRIVzQe1KBKi5LNKDPGim\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"GradeLabel\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 53\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 58\n      },\n      {\n        \"__id__\": 59\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 130.409,\n      \"y\": -112.772,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"4c6TBCyQNDh4u02uZVM9Ia\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 57\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 44,\n      \"height\": 54.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"94crO2NGVIBrKQMZlPhyGj\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 57\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 190,\n      \"g\": 255,\n      \"b\": 129,\n      \"a\": 255\n    },\n    \"_string\": \"评价\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 20,\n    \"_fontSize\": 20,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"3aYd1vFDlFv4SBAkEAfTix\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"lock\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 53\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 61\n      },\n      {\n        \"__id__\": 62\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.5,\n      \"y\": 0.5,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d2N8Va451IRK7Un8DUs27r\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 60\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 139,\n      \"height\": 186\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"281ve6Mt5G4bkz0/caIPbY\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 60\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"5c270278-c097-4dba-9f15-f3886683bff7@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"19GcfpVBxP6YsfcaJq05R8\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 53\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 300,\n      \"height\": 200\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"75FJOJgFpPVIYnTQU4Q60K\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 53\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"d0ade2fb-557f-4edb-a96c-d68e19c7c942@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"59ITRSQktLPITdsErgHfnq\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 53\n    },\n    \"_enabled\": false,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 2,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": {\n      \"__uuid__\": \"d0ade2fb-557f-4edb-a96c-d68e19c7c942@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_hoverSprite\": {\n      \"__uuid__\": \"20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_pressedSprite\": {\n      \"__uuid__\": \"544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_disabledSprite\": {\n      \"__uuid__\": \"951249e0-9f16-456d-8b85-a6ca954da16b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 53\n    },\n    \"_id\": \"45r3LYQ/pGlZK0uhneClh/\"\n  },\n  {\n    \"__type__\": \"cc.Toggle\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 53\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 0,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": null,\n    \"checkEvents\": [],\n    \"_isChecked\": false,\n    \"_checkMark\": {\n      \"__id__\": 56\n    },\n    \"_id\": \"4dUxB4IPpFSaBzDAIQg53d\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 24\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 996,\n      \"height\": 219.869\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"49BYzf6ytPlY97Hey+YZwe\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 24\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 0,\n    \"_layoutType\": 1,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 0,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 0,\n    \"_paddingBottom\": 0,\n    \"_spacingX\": 48,\n    \"_spacingY\": 0,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 0,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": true,\n    \"_id\": \"d05BMBjl9GqZKgd7Wo6ssJ\"\n  },\n  {\n    \"__type__\": \"cc.ToggleContainer\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 24\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_allowSwitchOff\": true,\n    \"checkEvents\": [],\n    \"_id\": \"35w4pVQ1VBrqdO+yEy8lCh\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 23\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 1053.689,\n      \"height\": 265.336\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"20wHk2nuxExKmYeAERuRiN\"\n  },\n  {\n    \"__type__\": \"cc.Mask\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 23\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_type\": 0,\n    \"_inverted\": false,\n    \"_segments\": 64,\n    \"_alphaThreshold\": 0.1,\n    \"_id\": \"2awtL3oyFAt4uu7R+qW1Us\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 23\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 1,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 0\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"47uFu7NpxHHKpEp3vmlsR3\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 22\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 1111.749,\n      \"height\": 269.823\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"a865tGFdRFjJezhu3GpcbD\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 22\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 208,\n      \"g\": 136,\n      \"b\": 226,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"28974bc3-f2e4-4a81-a402-d716d17c19a7@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"e3FOpksc9PzZZnrJN8ua8l\"\n  },\n  {\n    \"__type__\": \"cc.ScrollView\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 22\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"bounceDuration\": 0.23,\n    \"brake\": 0.75,\n    \"elastic\": true,\n    \"inertia\": true,\n    \"horizontal\": true,\n    \"vertical\": false,\n    \"cancelInnerEvents\": true,\n    \"scrollEvents\": [],\n    \"_content\": {\n      \"__id__\": 24\n    },\n    \"_horizontalScrollBar\": null,\n    \"_verticalScrollBar\": null,\n    \"_id\": \"5fE3g2KSJNirCMJ8njW8Kp\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"cars-scroll\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 77\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 140\n      },\n      {\n        \"__id__\": 141\n      },\n      {\n        \"__id__\": 142\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -143.253,\n      \"y\": -137.308,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"8bg7msYtlGsI8olByv0Xeq\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"view\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 76\n    },\n    \"_children\": [\n      {\n        \"__id__\": 78\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 137\n      },\n      {\n        \"__id__\": 138\n      },\n      {\n        \"__id__\": 139\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.651,\n      \"y\": -4.356,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e8tHk2t1FIFr/AEyYpWyPK\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"cars\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 77\n    },\n    \"_children\": [\n      {\n        \"__id__\": 79\n      },\n      {\n        \"__id__\": 90\n      },\n      {\n        \"__id__\": 101\n      },\n      {\n        \"__id__\": 112\n      },\n      {\n        \"__id__\": 123\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 134\n      },\n      {\n        \"__id__\": 135\n      },\n      {\n        \"__id__\": 136\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 228,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"04D8sfa/hF7ao+/Y9x7w+Y\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"car-1\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 78\n    },\n    \"_children\": [\n      {\n        \"__id__\": 80\n      },\n      {\n        \"__id__\": 83\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 87\n      },\n      {\n        \"__id__\": 88\n      },\n      {\n        \"__id__\": 89\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -496,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ebk2a8qiVDZq5zNudOwwbF\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"mark\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 79\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 81\n      },\n      {\n        \"__id__\": 82\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1.8,\n      \"y\": 1.8,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d388y9ijtFULqFs9FJ4neX\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 80\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 109.195,\n      \"height\": 119.036\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"002n8615JL7ru+jDAhUuyW\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 80\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"9368a3e2-29bf-4ad3-a422-77a08242544b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"96HA5rAHBDhIHc/ebNwMc1\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"PurchaseButton\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 79\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 84\n      },\n      {\n        \"__id__\": 85\n      },\n      {\n        \"__id__\": 86\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"3dx5Cfsw5NfoQz0raGIAks\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 83\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 40\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"adZlvk9D9CD4iSa15gU+cP\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 83\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"a50181e9-ecbd-4d36-bf53-3db514f24fee@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"4ffM88yUlAAYEA6nNyPK4V\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 83\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 3,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": {\n      \"__uuid__\": \"a50181e9-ecbd-4d36-bf53-3db514f24fee@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_hoverSprite\": {\n      \"__uuid__\": \"20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_pressedSprite\": {\n      \"__uuid__\": \"544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_disabledSprite\": {\n      \"__uuid__\": \"951249e0-9f16-456d-8b85-a6ca954da16b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 83\n    },\n    \"_id\": \"13lSdtvrdLkrkxVACtTiZS\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 79\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 124\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"8dkCOOyH9NsaI7JhxcXfx8\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 79\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"35990206-f1f7-49c0-91d6-a50921fc8128@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"7eFLep9v5GtqEiOcoHCZ4m\"\n  },\n  {\n    \"__type__\": \"cc.Toggle\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 79\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 0,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 79\n    },\n    \"checkEvents\": [],\n    \"_isChecked\": true,\n    \"_checkMark\": {\n      \"__id__\": 82\n    },\n    \"_id\": \"5aI8c25oNKGISrP/sv+ltI\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"car-2\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 78\n    },\n    \"_children\": [\n      {\n        \"__id__\": 91\n      },\n      {\n        \"__id__\": 94\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 98\n      },\n      {\n        \"__id__\": 99\n      },\n      {\n        \"__id__\": 100\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -248,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"03yOoKmhBJl4VpaxGMqHoy\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"mark\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 90\n    },\n    \"_children\": [],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 92\n      },\n      {\n        \"__id__\": 93\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1.8,\n      \"y\": 1.8,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"747JMhGFJP5b1bqUR4zE8i\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 91\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 109.195,\n      \"height\": 119.036\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"27vQHvt9RIt5JO2Hx29q0e\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 91\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"9368a3e2-29bf-4ad3-a422-77a08242544b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"661BO+YNlB85PC7ysAGzWv\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"PurchaseButton\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 90\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 95\n      },\n      {\n        \"__id__\": 96\n      },\n      {\n        \"__id__\": 97\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"95qekhmNNDCJDE7d3ya1YM\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 94\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 40\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"54X/SosQZDpqxKwFMUFd9/\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 94\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"a50181e9-ecbd-4d36-bf53-3db514f24fee@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"db9aFoxidLqpTdJWF9XwkR\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 94\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 3,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": {\n      \"__uuid__\": \"a50181e9-ecbd-4d36-bf53-3db514f24fee@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_hoverSprite\": {\n      \"__uuid__\": \"20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_pressedSprite\": {\n      \"__uuid__\": \"544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_disabledSprite\": {\n      \"__uuid__\": \"951249e0-9f16-456d-8b85-a6ca954da16b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 94\n    },\n    \"_id\": \"57jkL8rmVHwojOPQqbfu4v\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 90\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 124\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"bcN8mmrRdB94/w1lBb1dBz\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 90\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"5308cc80-fcde-4667-baa6-dbcdc87fd132@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"f5vTAjWztFtKTtqMeZsNss\"\n  },\n  {\n    \"__type__\": \"cc.Toggle\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 90\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 0,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 90\n    },\n    \"checkEvents\": [],\n    \"_isChecked\": false,\n    \"_checkMark\": {\n      \"__id__\": 93\n    },\n    \"_id\": \"ce9iwmXcFGSZpjpRZPU0vV\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"car-3\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 78\n    },\n    \"_children\": [\n      {\n        \"__id__\": 102\n      },\n      {\n        \"__id__\": 105\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 109\n      },\n      {\n        \"__id__\": 110\n      },\n      {\n        \"__id__\": 111\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"15VyCZmi9AXJJAtzmUSsW9\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"mark\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 101\n    },\n    \"_children\": [],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 103\n      },\n      {\n        \"__id__\": 104\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1.8,\n      \"y\": 1.8,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5caxzjr1dCab3EuvxJjLlY\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 102\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 109.195,\n      \"height\": 119.036\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"33htkcURBCiYCOhojAnnIL\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 102\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"9368a3e2-29bf-4ad3-a422-77a08242544b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"e1GwvAxX1A47PVdB/s/UPS\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"PurchaseButton\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 101\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 106\n      },\n      {\n        \"__id__\": 107\n      },\n      {\n        \"__id__\": 108\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"16Uzh64gpBELeMHmjzw+yD\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 105\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 40\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"17AuCcfEtG3qMbAb2Q2qr2\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 105\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"a50181e9-ecbd-4d36-bf53-3db514f24fee@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"fe+cP/YIJFX6h7CI1gMNkA\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 105\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 3,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": {\n      \"__uuid__\": \"a50181e9-ecbd-4d36-bf53-3db514f24fee@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_hoverSprite\": {\n      \"__uuid__\": \"20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_pressedSprite\": {\n      \"__uuid__\": \"544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_disabledSprite\": {\n      \"__uuid__\": \"951249e0-9f16-456d-8b85-a6ca954da16b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 105\n    },\n    \"_id\": \"fbFGLd9a9A16WyfBSKRBTR\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 101\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 124\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"48SwvNPnRMx7il36/aZArp\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 101\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"a9e97b11-149f-46e6-a990-c95fd6d91cd7@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"eamqRjCfFPEbiruH69R6a4\"\n  },\n  {\n    \"__type__\": \"cc.Toggle\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 101\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 0,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": null,\n    \"checkEvents\": [],\n    \"_isChecked\": false,\n    \"_checkMark\": {\n      \"__id__\": 104\n    },\n    \"_id\": \"74g2si/UdG4bDRHMPY1r6u\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"car-4\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 78\n    },\n    \"_children\": [\n      {\n        \"__id__\": 113\n      },\n      {\n        \"__id__\": 116\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 120\n      },\n      {\n        \"__id__\": 121\n      },\n      {\n        \"__id__\": 122\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 248,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"950dch+pZFh6iX+vKEFiPU\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"mark\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 112\n    },\n    \"_children\": [],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 114\n      },\n      {\n        \"__id__\": 115\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1.8,\n      \"y\": 1.8,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"034MMRsS1F5pEWAqc7L1bS\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 113\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 109.195,\n      \"height\": 119.036\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"84ROL9R7dIIZz8phfzFBq/\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 113\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"9368a3e2-29bf-4ad3-a422-77a08242544b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"5fh0pwuAJNiaxu+T9WNK/N\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"PurchaseButton\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 112\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 117\n      },\n      {\n        \"__id__\": 118\n      },\n      {\n        \"__id__\": 119\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ba3kz5TvpME7Ma4qo51SaV\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 116\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 40\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"edrVAfphNKgbXBRkkoLUY6\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 116\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"a50181e9-ecbd-4d36-bf53-3db514f24fee@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"5aieILCy9PfYF16mY5sHk+\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 116\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 3,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 116\n    },\n    \"_id\": \"0balWENG5Cmp8ZIo2XkPTE\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 112\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 124\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"ef+80Icj1N74MFIStAGLyF\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 112\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"0b33dc38-0c13-4f55-b754-27d76610db59@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"9eqcRKS3tIy5rLge664jSr\"\n  },\n  {\n    \"__type__\": \"cc.Toggle\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 112\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 0,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": null,\n    \"checkEvents\": [],\n    \"_isChecked\": false,\n    \"_checkMark\": {\n      \"__id__\": 115\n    },\n    \"_id\": \"bcF4qNlodHlbpi6gNbQHNp\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"car-5\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 78\n    },\n    \"_children\": [\n      {\n        \"__id__\": 124\n      },\n      {\n        \"__id__\": 127\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 131\n      },\n      {\n        \"__id__\": 132\n      },\n      {\n        \"__id__\": 133\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 496,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"3b0LpuRvVIxLcZtaIabPsT\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"mark\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 123\n    },\n    \"_children\": [],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 125\n      },\n      {\n        \"__id__\": 126\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1.8,\n      \"y\": 1.8,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"77U7ZVkJ5GzqsU5kYcDYmB\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 124\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 109.195,\n      \"height\": 119.036\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"eaCbXoWkBBIaXFX4EhxNq7\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 124\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"9368a3e2-29bf-4ad3-a422-77a08242544b@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"felerQCPJPdqE3Ub0CgIJr\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"PurchaseButton\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 123\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 128\n      },\n      {\n        \"__id__\": 129\n      },\n      {\n        \"__id__\": 130\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"97wwXG5p1BMZmrY0rFliWe\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 127\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 40\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"20qx978K5Lg7rCnz6QwuzW\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 127\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"a50181e9-ecbd-4d36-bf53-3db514f24fee@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"15MweU2vJLpJf+0SckvLJm\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 127\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 3,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 127\n    },\n    \"_id\": \"49MVTOhAlGg5VYSrd8ZbNf\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 123\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 124\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"edGhQp1hhGx49ocw28UBZp\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 123\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"7e95e032-b6ae-4de9-a8ab-c81b24b5fb5f@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"bfj3O+p5pNcrzMNIuBkSsm\"\n  },\n  {\n    \"__type__\": \"cc.Toggle\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 123\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 0,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": null,\n    \"checkEvents\": [],\n    \"_isChecked\": false,\n    \"_checkMark\": {\n      \"__id__\": 126\n    },\n    \"_id\": \"9d2PCAqnZNXIDnIYhNH9lA\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 78\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 1192,\n      \"height\": 120\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"40hDoalt5JIq1qxaEmmJAS\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 78\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 1,\n    \"_layoutType\": 1,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 0,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 0,\n    \"_paddingBottom\": 0,\n    \"_spacingX\": 48,\n    \"_spacingY\": 0,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 0,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": true,\n    \"_id\": \"d9Q6gYMzxHDb7O0M0eNM84\"\n  },\n  {\n    \"__type__\": \"cc.ToggleContainer\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 78\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_allowSwitchOff\": true,\n    \"checkEvents\": [],\n    \"_id\": \"34ZuaVlOpCAKQ0ZrqpSlkl\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 77\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 735.317,\n      \"height\": 250\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"38hlYvdkNFco+oGqt0gy7L\"\n  },\n  {\n    \"__type__\": \"cc.Mask\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 77\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_type\": 0,\n    \"_inverted\": false,\n    \"_segments\": 64,\n    \"_alphaThreshold\": 0.1,\n    \"_id\": \"f8GBQL5GdFa61lU44STkeX\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 77\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 1,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 0\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"53uut704JOKpQkU45fBbCK\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 76\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 731.981,\n      \"height\": 217.89299999999997\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"32eX3e8cxMbZML6CuH4iLw\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 76\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"28974bc3-f2e4-4a81-a402-d716d17c19a7@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"f955trfMZLCoIJGKKHpAR1\"\n  },\n  {\n    \"__type__\": \"cc.ScrollView\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 76\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"bounceDuration\": 0.23,\n    \"brake\": 0.75,\n    \"elastic\": true,\n    \"inertia\": true,\n    \"horizontal\": true,\n    \"vertical\": false,\n    \"cancelInnerEvents\": true,\n    \"scrollEvents\": [],\n    \"_content\": {\n      \"__id__\": 78\n    },\n    \"_horizontalScrollBar\": null,\n    \"_verticalScrollBar\": null,\n    \"_id\": \"03ZrnoU+5NWZePw6zDE8Gr\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"startbutton\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 144\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 147\n      },\n      {\n        \"__id__\": 148\n      },\n      {\n        \"__id__\": 149\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 226.081,\n      \"y\": -292.553,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d0VSizKCNNOp/4TQYfehby\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 143\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 145\n      },\n      {\n        \"__id__\": 146\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"13NE85iVRKjbriffd5UYkL\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 144\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 80\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"754rLExyNH15bxxYlu0g1E\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 144\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 12,\n      \"b\": 148,\n      \"a\": 255\n    },\n    \"_string\": \"start\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": false,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": false,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"9977umiCpOGobudd4zHfls\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 143\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 80\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"89cO4pK2hGP7BLL5XKpR1X\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 143\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"4cf1948a-ffca-426e-9c43-1e7c864a1b60@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"c1EEbduAZBuY0n6Rb+qz+q\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 143\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 3,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 143\n    },\n    \"_id\": \"fc1Sh8juRGSo3Yy1bhO4t/\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"backbutton\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 151\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 154\n      },\n      {\n        \"__id__\": 155\n      },\n      {\n        \"__id__\": 156\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -284.139,\n      \"y\": -292.553,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"33Pc5dh7hIRZxLEkCXLlkL\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 150\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 152\n      },\n      {\n        \"__id__\": 153\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"46hP8zEy5GkrUBZxSeq+hK\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 151\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 80\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"87Dn5PQVhB2rUvDsuKGhgG\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 151\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 234,\n      \"g\": 255,\n      \"b\": 122,\n      \"a\": 255\n    },\n    \"_string\": \"back\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 40,\n    \"_fontSize\": 40,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 1,\n    \"_enableWrapText\": false,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"bf3P6Uu7BMkogy1mDHJFgA\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 150\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 200,\n      \"height\": 80\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"e3c/IaCklOTK37N7fCpHmf\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 150\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"593904f2-fa85-498f-9b0e-9f79890c24fd@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"0cmp4Ww6FPZZO/gt37V5rE\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 150\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 3,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 214,\n      \"g\": 214,\n      \"b\": 214,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": {\n      \"__id__\": 150\n    },\n    \"_id\": \"50mTSJtQNJJKKqYmNamO5M\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"car-property\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 158\n      },\n      {\n        \"__id__\": 170\n      },\n      {\n        \"__id__\": 182\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 194\n      },\n      {\n        \"__id__\": 195\n      },\n      {\n        \"__id__\": 196\n      },\n      {\n        \"__id__\": 197\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 430.4145,\n      \"y\": -135.665,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"6eWrrL9zJHiqI3/MEjL4pp\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"speed\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 157\n    },\n    \"_children\": [\n      {\n        \"__id__\": 159\n      },\n      {\n        \"__id__\": 162\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 169\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -97.013,\n      \"y\": 61.800000000000004,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"f6KDmbD55GAK7L8M7HkQOR\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"speed\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 158\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 160\n      },\n      {\n        \"__id__\": 161\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"7cZWNVDhFI3bCgFaQPhyHu\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 159\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 101.802734375,\n      \"height\": 54.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"4db6U/P0xEH7Xut9+py2Ew\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 159\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_string\": \"speed速度\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 20,\n    \"_fontSize\": 20,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"66hICiZUxAD4XpWpzttxNH\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"ProgressBar\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 158\n    },\n    \"_children\": [\n      {\n        \"__id__\": 163\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 166\n      },\n      {\n        \"__id__\": 167\n      },\n      {\n        \"__id__\": 168\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 132.272,\n      \"y\": -2.471,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.5,\n      \"y\": 0.5,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"deilXS6bNCxru67oT9Br7b\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Bar\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 162\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 164\n      },\n      {\n        \"__id__\": 165\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -150,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b8fGCtAaJOda83dNkDNznS\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 163\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 15\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0.5\n    },\n    \"_id\": \"24B05j55ZLxIWLShuwQq5h\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 163\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 250,\n      \"g\": 75,\n      \"b\": 75,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"24a704da-2867-446d-8d1a-5e920c75e09d@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"4bzvy07TxDubMC9b5Vz+Sn\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 162\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 300,\n      \"height\": 15\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"abXyVf2CZH+LDa28IDKYpM\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 162\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 201,\n      \"g\": 255,\n      \"b\": 205,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"9fd900dd-221b-4f89-8f2c-fba34243c835@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"98QcpTW1BCZ7Bj6OSBkYYM\"\n  },\n  {\n    \"__type__\": \"cc.ProgressBar\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 162\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_barSprite\": {\n      \"__id__\": 165\n    },\n    \"_mode\": 0,\n    \"_totalLength\": 300,\n    \"_progress\": 0.2,\n    \"_reverse\": false,\n    \"_id\": \"f3nJME/HFNNoyS/bhJgk3m\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 158\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 30\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"81t3OGi5RJlakIjtNmUaj+\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"turn\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 157\n    },\n    \"_children\": [\n      {\n        \"__id__\": 171\n      },\n      {\n        \"__id__\": 174\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 181\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -97.013,\n      \"y\": 11.700000000000003,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"2bK7YvetNLcKl3prd/hcGY\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"turn\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 170\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 172\n      },\n      {\n        \"__id__\": 173\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -9.216,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"5eOvyf0VFNOJjPyj3FC95U\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 171\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 82.876953125,\n      \"height\": 54.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"72cYlDbMJFb6iR/rB8qSAH\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 171\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 243,\n      \"b\": 102,\n      \"a\": 255\n    },\n    \"_string\": \"turn转向\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 20,\n    \"_fontSize\": 20,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"7953Cw3lBN4qUH63TTpp85\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"ProgressBar\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 170\n    },\n    \"_children\": [\n      {\n        \"__id__\": 175\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 178\n      },\n      {\n        \"__id__\": 179\n      },\n      {\n        \"__id__\": 180\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 132.439,\n      \"y\": -2.471,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.5,\n      \"y\": 0.5,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"9cbEnJYDxLrL8SLMOl82D7\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Bar\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 174\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 176\n      },\n      {\n        \"__id__\": 177\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -150,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"c733Oy1JxMxLU48zS2498q\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 175\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 15\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0.5\n    },\n    \"_id\": \"d1EFMlMbxHLarQWrYuDFEK\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 175\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 251,\n      \"b\": 163,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"24a704da-2867-446d-8d1a-5e920c75e09d@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"7fgxgyS0xGjZnPTEqYE/+4\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 174\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 300,\n      \"height\": 15\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"264fLIEWxAhqy0UEi/zP2l\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 174\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 138,\n      \"g\": 138,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"9fd900dd-221b-4f89-8f2c-fba34243c835@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"5fWhG3TrhHnIf+JmzxCzP3\"\n  },\n  {\n    \"__type__\": \"cc.ProgressBar\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 174\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_barSprite\": {\n      \"__id__\": 177\n    },\n    \"_mode\": 0,\n    \"_totalLength\": 300,\n    \"_progress\": 0.2,\n    \"_reverse\": false,\n    \"_id\": \"d1qiShtvJLhZWxNrYWct7E\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 170\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 30\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d22ntZJwxPCo4QfD/w7VBP\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"tough\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 157\n    },\n    \"_children\": [\n      {\n        \"__id__\": 183\n      },\n      {\n        \"__id__\": 186\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 193\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -97.013,\n      \"y\": -38.4,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"e8l3LLGKdCH6dFDR4kSDU4\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"tough\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 182\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 184\n      },\n      {\n        \"__id__\": 185\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"78RMEOrKlG7Jd6GEDutoRd\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 183\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 99.52734375,\n      \"height\": 54.4\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c6dZ1i2TdJuI9M+HZNXbUP\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 183\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 10,\n      \"g\": 255,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_string\": \"tough坚硬\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 20,\n    \"_fontSize\": 20,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"f4rBhbQIpGMp9xagwRRSWo\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"ProgressBar\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 182\n    },\n    \"_children\": [\n      {\n        \"__id__\": 187\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 190\n      },\n      {\n        \"__id__\": 191\n      },\n      {\n        \"__id__\": 192\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 134.433,\n      \"y\": -2.471,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0.5,\n      \"y\": 0.5,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"39uuMbilxA5ah6AeH4aJD4\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Bar\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 186\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 188\n      },\n      {\n        \"__id__\": 189\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -150,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"94KOUpFyFDCa5ypCf3cFpe\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 187\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 60,\n      \"height\": 15\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0.5\n    },\n    \"_id\": \"3832vij5tMh7xzy+rsZzeJ\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 187\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 154,\n      \"g\": 255,\n      \"b\": 107,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"24a704da-2867-446d-8d1a-5e920c75e09d@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"c9lv9H6+ZCc51ry7gm3+ll\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 186\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 300,\n      \"height\": 15\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"88T22loTBKxKa7gbzjtcfS\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 186\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 221,\n      \"b\": 252,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"9fd900dd-221b-4f89-8f2c-fba34243c835@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 1,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"91uBO7+HlOAJvuI7nJp0Fu\"\n  },\n  {\n    \"__type__\": \"cc.ProgressBar\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 186\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_barSprite\": {\n      \"__id__\": 189\n    },\n    \"_mode\": 0,\n    \"_totalLength\": 300,\n    \"_progress\": 0.2,\n    \"_reverse\": false,\n    \"_id\": \"4fC5LhDP9PNozVSvu0P39n\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 182\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 100,\n      \"height\": 30\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"77LO7p5fxPDKPckmR67jxM\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 157\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 334.683,\n      \"height\": 200\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"18uaYkPTZHRLt4la36Q544\"\n  },\n  {\n    \"__type__\": \"cc.Layout\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 157\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_resizeMode\": 0,\n    \"_layoutType\": 2,\n    \"_cellSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 40,\n      \"height\": 40\n    },\n    \"_startAxis\": 0,\n    \"_paddingLeft\": 0,\n    \"_paddingRight\": 0,\n    \"_paddingTop\": 23.2,\n    \"_paddingBottom\": 1,\n    \"_spacingX\": 0,\n    \"_spacingY\": 20.1,\n    \"_verticalDirection\": 1,\n    \"_horizontalDirection\": 0,\n    \"_constraint\": 0,\n    \"_constraintNum\": 2,\n    \"_affectedByScale\": false,\n    \"_isAlign\": false,\n    \"_id\": \"78qPUkJ7tMYrG82acQVM92\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 157\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"28974bc3-f2e4-4a81-a402-d716d17c19a7@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"a4jnTcauJLlq5SUmHoXbgU\"\n  },\n  {\n    \"__type__\": \"add91PL8SRFs5KSXc8GbptH\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 157\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"speedProgressBar\": {\n      \"__id__\": 168\n    },\n    \"steeringProgressBar\": {\n      \"__id__\": 180\n    },\n    \"durabilityProgressBar\": {\n      \"__id__\": 192\n    },\n    \"enableAnimation\": true,\n    \"animationDuration\": 0.5,\n    \"_id\": \"11Uc2T/UpJl7yBgn9SrzfU\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"PurchasePanel\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 199\n      },\n      {\n        \"__id__\": 203\n      },\n      {\n        \"__id__\": 210\n      },\n      {\n        \"__id__\": 213\n      }\n    ],\n    \"_active\": false,\n    \"_components\": [\n      {\n        \"__id__\": 216\n      },\n      {\n        \"__id__\": 217\n      },\n      {\n        \"__id__\": 218\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"44WwC9ggVAGKhMZxHahE30\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"close\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 198\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 200\n      },\n      {\n        \"__id__\": 201\n      },\n      {\n        \"__id__\": 202\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 212.483,\n      \"y\": 235.404,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"14KlTOGatLCZJtn5oH4pPB\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 199\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 55.50500000000002,\n      \"height\": 55.50500000000002\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"73McuO/LdEJrHJ8XDXzaqm\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 199\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"a426c2c8-58b1-4e5a-a16c-e75061a2d034@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"f7UR9QNAxKCaerfILeUR/p\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 199\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 3,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": null,\n    \"_id\": \"b2//ffMGpNIoe++9SzBXpZ\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"BuyButton\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 198\n    },\n    \"_children\": [\n      {\n        \"__id__\": 204\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 207\n      },\n      {\n        \"__id__\": 208\n      },\n      {\n        \"__id__\": 209\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": -162.386,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ddVOZPWDxAU4dFVfulGIZd\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Label\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 203\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 205\n      },\n      {\n        \"__id__\": 206\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"69fHwgxgBH9YqLK908CgOU\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 204\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 181.181640625,\n      \"height\": 56.047999999999995\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"e5AMsqIEtF97d5ogq8Cyp+\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 204\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 158,\n      \"g\": 98,\n      \"b\": 42,\n      \"a\": 255\n    },\n    \"_string\": \"确认购买\\npruchase comfirmed\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 20,\n    \"_fontSize\": 20,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 24.8,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": false,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": false,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": false,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"f9fXq+Z+pOsKjJUztxwCwS\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 203\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 280.995,\n      \"height\": 81.87200000000001\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"57iNw/FB9P4J6KSrawYa/8\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 203\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"4cf1948a-ffca-426e-9c43-1e7c864a1b60@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 0,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"b4L5ex60VA1Y4cR6FtccNt\"\n  },\n  {\n    \"__type__\": \"cc.Button\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 203\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"clickEvents\": [],\n    \"_interactable\": true,\n    \"_transition\": 3,\n    \"_normalColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_hoverColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 211,\n      \"g\": 211,\n      \"b\": 211,\n      \"a\": 255\n    },\n    \"_pressedColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_disabledColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 124,\n      \"g\": 124,\n      \"b\": 124,\n      \"a\": 255\n    },\n    \"_normalSprite\": null,\n    \"_hoverSprite\": null,\n    \"_pressedSprite\": null,\n    \"_disabledSprite\": null,\n    \"_duration\": 0.1,\n    \"_zoomScale\": 1.2,\n    \"_target\": null,\n    \"_id\": \"calxvDuclCworqSmJ1Xox3\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"PriceText\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 198\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 211\n      },\n      {\n        \"__id__\": 212\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 204.572,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"52sXXJjmJIzJC3t3W4n4l1\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 210\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 240.215234375,\n      \"height\": 99.99999999999999\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"f8TCo3lmZJPaSg8JVXhUtQ\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 210\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 216,\n      \"g\": 4,\n      \"b\": 182,\n      \"a\": 255\n    },\n    \"_string\": \"该车辆价格为：1000\\n是否确认购买？\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 25,\n    \"_fontSize\": 25,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 255,\n      \"b\": 204,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 4.8,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"b9iPdSUBZL+aKmZvfWZG/J\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"infoText\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 198\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 214\n      },\n      {\n        \"__id__\": 215\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 2.697,\n      \"y\": 81.515,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"b5s06CHPpMYLsiN83hrpBF\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 213\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 482.38964013671875,\n      \"height\": 140.66799999999998\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"ef4p/kGN9DZb45HLAdVL1f\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 213\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 216,\n      \"g\": 4,\n      \"b\": 182,\n      \"a\": 255\n    },\n    \"_string\": \"This super maneuverable car is equipped with a bullet launcher. When you hit your opponent, you can cause damage.\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 25,\n    \"_fontSize\": 25,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 31.8,\n    \"_overflow\": 3,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 238,\n      \"g\": 199,\n      \"b\": 44,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2.6,\n    \"_enableShadow\": true,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"9buTQi98RDZbZ7Cu4M5pxD\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 198\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 509,\n      \"height\": 558\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"8bCEFbRvdDjKVtmUwoEOI0\"\n  },\n  {\n    \"__type__\": \"cc.Sprite\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 198\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_spriteFrame\": {\n      \"__uuid__\": \"6a0179db-b0bc-4905-8953-8ab076a3cfd6@f9941\",\n      \"__expectedType__\": \"cc.SpriteFrame\"\n    },\n    \"_type\": 0,\n    \"_fillType\": 0,\n    \"_sizeMode\": 1,\n    \"_fillCenter\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 0\n    },\n    \"_fillStart\": 0,\n    \"_fillRange\": 0,\n    \"_isTrimmedMode\": true,\n    \"_useGrayscale\": false,\n    \"_atlas\": null,\n    \"_id\": \"efEauvsN9JHLl1Kg0jaxyB\"\n  },\n  {\n    \"__type__\": \"c4e94PebKNGoJIxF8tD6ORW\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 198\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"priceLabel\": {\n      \"__id__\": 212\n    },\n    \"infoLabel\": {\n      \"__id__\": 215\n    },\n    \"closeButton\": {\n      \"__id__\": 202\n    },\n    \"confirmButton\": {\n      \"__id__\": 209\n    },\n    \"_id\": \"49l/ZOogdKbrXTaHLVai+A\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"notice\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 220\n      },\n      {\n        \"__id__\": 221\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -32.114,\n      \"y\": 295.904,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"feuekM+6dBBLU7AxbTAlUe\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 219\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 252.88671875,\n      \"height\": 94.39999999999999\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d8JZ90mlhB3pUNGXnC4cii\"\n  },\n  {\n    \"__type__\": \"cc.Label\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 219\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 171,\n      \"g\": 16,\n      \"b\": 219,\n      \"a\": 255\n    },\n    \"_string\": \"拥有的金币不够\\nyout money is not enough\",\n    \"_horizontalAlign\": 1,\n    \"_verticalAlign\": 1,\n    \"_actualFontSize\": 20,\n    \"_fontSize\": 20,\n    \"_fontFamily\": \"Arial\",\n    \"_lineHeight\": 40,\n    \"_overflow\": 0,\n    \"_enableWrapText\": true,\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_spacingX\": 0,\n    \"_isItalic\": false,\n    \"_isBold\": true,\n    \"_isUnderline\": false,\n    \"_underlineHeight\": 2,\n    \"_cacheMode\": 0,\n    \"_enableOutline\": true,\n    \"_outlineColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_outlineWidth\": 2,\n    \"_enableShadow\": false,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_shadowOffset\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 2,\n      \"y\": 2\n    },\n    \"_shadowBlur\": 2,\n    \"_id\": \"3dfXK2mP9GlqJGIo1luu5n\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_prefab\": {\n      \"__id__\": 223\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 222\n    },\n    \"asset\": {\n      \"__uuid__\": \"81520cf5-3a02-463b-b3ec-c8870a3f4940\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"98m1scCCpHFaAD50RjkFIZ\",\n    \"instance\": {\n      \"__id__\": 224\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"bbGyGdOA5B3ZJj3RjG/RpL\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 225\n      },\n      {\n        \"__id__\": 227\n      },\n      {\n        \"__id__\": 229\n      },\n      {\n        \"__id__\": 231\n      },\n      {\n        \"__id__\": 233\n      },\n      {\n        \"__id__\": 235\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 226\n    },\n    \"propertyPath\": [\n      \"_name\"\n    ],\n    \"value\": \"loading\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"98m1scCCpHFaAD50RjkFIZ\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 228\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1.672,\n      \"y\": 16.154,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"98m1scCCpHFaAD50RjkFIZ\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 230\n    },\n    \"propertyPath\": [\n      \"_lrot\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"98m1scCCpHFaAD50RjkFIZ\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 232\n    },\n    \"propertyPath\": [\n      \"_euler\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"98m1scCCpHFaAD50RjkFIZ\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 234\n    },\n    \"propertyPath\": [\n      \"_color\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    }\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"50p6T0zeZHzqrT8IjFBzum\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 236\n    },\n    \"propertyPath\": [\n      \"_active\"\n    ],\n    \"value\": true\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"98m1scCCpHFaAD50RjkFIZ\"\n    ]\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 1280,\n      \"height\": 720\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d6rUX5yfhMlKoWX2bSbawx\"\n  },\n  {\n    \"__type__\": \"cc.Canvas\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_cameraComponent\": {\n      \"__id__\": 4\n    },\n    \"_alignCanvasWithScreen\": true,\n    \"_id\": \"12O/ljcVlEqLmVm3U2gEOQ\"\n  },\n  {\n    \"__type__\": \"cc.Widget\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_alignFlags\": 45,\n    \"_target\": null,\n    \"_left\": 0,\n    \"_right\": 0,\n    \"_top\": 5.684341886080802e-14,\n    \"_bottom\": 5.684341886080802e-14,\n    \"_horizontalCenter\": 0,\n    \"_verticalCenter\": 0,\n    \"_isAbsLeft\": true,\n    \"_isAbsRight\": true,\n    \"_isAbsTop\": true,\n    \"_isAbsBottom\": true,\n    \"_isAbsHorizontalCenter\": true,\n    \"_isAbsVerticalCenter\": true,\n    \"_originalWidth\": 0,\n    \"_originalHeight\": 0,\n    \"_alignMode\": 2,\n    \"_lockFlags\": 0,\n    \"_id\": \"c5V1EV8IpMtrIvY1OE9t2u\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"UIManager\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 1\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 241\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"1aPrVcYy1KLKgnxDGm5GUZ\"\n  },\n  {\n    \"__type__\": \"66fbclPUjBL7oYA2xIdTwf+\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 240\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"levelLabel\": null,\n    \"moneyLabel\": {\n      \"__id__\": 20\n    },\n    \"_id\": \"f48ntueglJlLCivE1C+Ko6\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"SelectManager\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 1\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 243\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"camwNZlClA7a7gD6K8R87I\"\n  },\n  {\n    \"__type__\": \"be7b23A2jVN6agcMGkP3NKP\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 242\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"levelToggleGroup\": {\n      \"__id__\": 69\n    },\n    \"carToggleGroup\": {\n      \"__id__\": 136\n    },\n    \"startButton\": {\n      \"__id__\": 149\n    },\n    \"backButton\": {\n      \"__id__\": 156\n    },\n    \"insufficientMoneyLabel\": {\n      \"__id__\": 221\n    },\n    \"purchasePanelNode\": {\n      \"__id__\": 198\n    },\n    \"carPropertyDisplay\": {\n      \"__id__\": 197\n    },\n    \"_id\": \"0cW6qdzxhMYLwNQpqT496d\"\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": null,\n    \"asset\": null,\n    \"fileId\": \"091c5c0e-b72a-4cad-ab68-635bc57ff236\",\n    \"instance\": null,\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": [\n      {\n        \"__id__\": 222\n      }\n    ]\n  },\n  {\n    \"__type__\": \"cc.SceneGlobals\",\n    \"ambient\": {\n      \"__id__\": 246\n    },\n    \"shadows\": {\n      \"__id__\": 247\n    },\n    \"_skybox\": {\n      \"__id__\": 248\n    },\n    \"fog\": {\n      \"__id__\": 249\n    },\n    \"octree\": {\n      \"__id__\": 250\n    },\n    \"skin\": {\n      \"__id__\": 251\n    },\n    \"lightProbeInfo\": {\n      \"__id__\": 252\n    },\n    \"postSettings\": {\n      \"__id__\": 253\n    },\n    \"bakedWithStationaryMainLight\": false,\n    \"bakedWithHighpLightmap\": false\n  },\n  {\n    \"__type__\": \"cc.AmbientInfo\",\n    \"_skyColorHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyColor\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyIllumHDR\": 20000,\n    \"_skyIllum\": 20000,\n    \"_groundAlbedoHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_groundAlbedo\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_skyColorLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.5,\n      \"z\": 0.8,\n      \"w\": 1\n    },\n    \"_skyIllumLDR\": 20000,\n    \"_groundAlbedoLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.2,\n      \"z\": 0.2,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.ShadowsInfo\",\n    \"_enabled\": false,\n    \"_type\": 0,\n    \"_normal\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 1,\n      \"z\": 0\n    },\n    \"_distance\": 0,\n    \"_planeBias\": 1,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 76,\n      \"g\": 76,\n      \"b\": 76,\n      \"a\": 255\n    },\n    \"_maxReceived\": 4,\n    \"_size\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 512,\n      \"y\": 512\n    }\n  },\n  {\n    \"__type__\": \"cc.SkyboxInfo\",\n    \"_envLightingType\": 0,\n    \"_envmapHDR\": null,\n    \"_envmap\": null,\n    \"_envmapLDR\": null,\n    \"_diffuseMapHDR\": null,\n    \"_diffuseMapLDR\": null,\n    \"_enabled\": false,\n    \"_useHDR\": true,\n    \"_editableMaterial\": null,\n    \"_reflectionHDR\": null,\n    \"_reflectionLDR\": null,\n    \"_rotationAngle\": 0\n  },\n  {\n    \"__type__\": \"cc.FogInfo\",\n    \"_type\": 0,\n    \"_fogColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 200,\n      \"g\": 200,\n      \"b\": 200,\n      \"a\": 255\n    },\n    \"_enabled\": false,\n    \"_fogDensity\": 0.3,\n    \"_fogStart\": 0.5,\n    \"_fogEnd\": 300,\n    \"_fogAtten\": 5,\n    \"_fogTop\": 1.5,\n    \"_fogRange\": 1.2,\n    \"_accurate\": false\n  },\n  {\n    \"__type__\": \"cc.OctreeInfo\",\n    \"_enabled\": false,\n    \"_minPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -1024,\n      \"y\": -1024,\n      \"z\": -1024\n    },\n    \"_maxPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1024,\n      \"y\": 1024,\n      \"z\": 1024\n    },\n    \"_depth\": 8\n  },\n  {\n    \"__type__\": \"cc.SkinInfo\",\n    \"_enabled\": false,\n    \"_blurRadius\": 0.01,\n    \"_sssIntensity\": 3\n  },\n  {\n    \"__type__\": \"cc.LightProbeInfo\",\n    \"_giScale\": 1,\n    \"_giSamples\": 1024,\n    \"_bounces\": 2,\n    \"_reduceRinging\": 0,\n    \"_showProbe\": true,\n    \"_showWireframe\": true,\n    \"_showConvex\": false,\n    \"_data\": null,\n    \"_lightProbeSphereVolume\": 1\n  },\n  {\n    \"__type__\": \"cc.PostSettingsInfo\",\n    \"_toneMappingType\": 0\n  }\n]"