[{"__type__": "cc.Prefab", "_name": "level-3", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "level-3", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}, {"__id__": 14}, {"__id__": 116}], "_active": true, "_components": [{"__id__": 162}, {"__id__": 164}, {"__id__": 166}, {"__id__": 168}, {"__id__": 170}, {"__id__": 172}], "_prefab": {"__id__": 174}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15.799, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "4d5329e9-8f67-43e3-a399-e0fa9a5ad5f4", "__expectedType__": "cc.Prefab"}, "fileId": "793wXpE0dMZZF5oHFdhgzy", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c7ptlcvJ1B8oUI4ZP7h749", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "PaintRoot"}, {"__type__": "cc.TargetInfo", "localID": ["793wXpE0dMZZF5oHFdhgzy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -639.97, "y": -359.883, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "BulletRoot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 11}], "_prefab": {"__id__": 13}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 4, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 12}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fb0w17IjtKzIPsuAwhzNPj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66KS9FnnFAzp7L5GiMkxzK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "cars", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 15}, {"__id__": 41}, {"__id__": 65}, {"__id__": 89}], "_active": true, "_components": [{"__id__": 113}], "_prefab": {"__id__": 115}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -276.598, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "car-1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [{"__id__": 16}], "_active": true, "_components": [{"__id__": 30}, {"__id__": 32}, {"__id__": 34}, {"__id__": 36}, {"__id__": 38}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": -610.581, "y": 344.475, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.**********865475, "w": 0.**********865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 2, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 15}, "_prefab": {"__id__": 17}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 16}, "asset": {"__uuid__": "5fa4a020-ad12-4d51-9e7d-57cecdc056ca", "__expectedType__": "cc.Prefab"}, "fileId": "40tQNkNPtP4aURzO7VMMIR", "instance": {"__id__": 18}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "ecF+pvTFxDA4n5kL51Gtxr", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 19}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 26}, {"__id__": 28}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_name"], "value": "healthBar"}, {"__type__": "cc.TargetInfo", "localID": ["40tQNkNPtP4aURzO7VMMIR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -35, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.**********865475, "w": 0.**********865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["offsetY"], "value": 50}, {"__type__": "cc.TargetInfo", "localID": ["88S4hp27BPFpL6l7nafPlR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 300, "height": 15}}, {"__type__": "cc.TargetInfo", "localID": ["27NHqliW9Fb4bDUyccWJj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 29}, "propertyPath": ["_progress"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 27.8, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15W75ZvFVLX4abw/6l2/OG"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 33}, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 1, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93j7SzUitAWKz60YV2JexH"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 35}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 1, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 27.8, "height": 60.1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aH+r+uQZEA4UF98ujueQt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 37}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49e36654-b2c3-47d9-afeb-5db28286a1b0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ad89w3uVNKW5W61hU9PuBC"}, {"__type__": "91c082Rgh1HBbV2vKQl2J1W", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 39}, "maxSpeed": 10, "acceleration": 10, "brakeDeceleration": 200, "turnSpeed": 50, "friction": 1.5, "initAngle": 0, "maxHealth": 30, "healthBar": null, "destroyedSprite": {"__uuid__": "86087b4b-4284-4b91-8707-fa1f71406ea1@f9941", "__expectedType__": "cc.SpriteFrame"}, "removeDelay": 3, "paintPrefab": {"__uuid__": "b71c19d0-4ac9-4dcb-aa32-b65fde2c406a", "__expectedType__": "cc.Prefab"}, "paintSprayInterval": 0.2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4clY0r5VDU72U+gRnPaqq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4Ce+Pis1BOL5qu2B4jBnq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "car-2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [{"__id__": 42}], "_active": true, "_components": [{"__id__": 54}, {"__id__": 56}, {"__id__": 58}, {"__id__": 60}, {"__id__": 62}], "_prefab": {"__id__": 64}, "_lpos": {"__type__": "cc.Vec3", "x": 609.901, "y": 338.207, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.**********865475, "w": 0.**********865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 2, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 41}, "_prefab": {"__id__": 43}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 42}, "asset": {"__uuid__": "5fa4a020-ad12-4d51-9e7d-57cecdc056ca", "__expectedType__": "cc.Prefab"}, "fileId": "40tQNkNPtP4aURzO7VMMIR", "instance": {"__id__": 44}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "bfdc8M3EpEkpLqR3RzQ9vT", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 45}, {"__id__": 47}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}, {"__id__": 52}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_name"], "value": "healthBar"}, {"__type__": "cc.TargetInfo", "localID": ["40tQNkNPtP4aURzO7VMMIR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 35, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.**********865475, "w": 0.**********865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 300, "height": 15}}, {"__type__": "cc.TargetInfo", "localID": ["27NHqliW9Fb4bDUyccWJj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 53}, "propertyPath": ["_progress"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 55}, "_contentSize": {"__type__": "cc.Size", "width": 27.8, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dR5BcAr5PvZKcEL4KFpN2"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 57}, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 1, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cftW7C8TRBLIaAIx5A6Zg3"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 59}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 1, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 27.8, "height": 60.1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45Iup+wz9N566a6jKIcsRg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 61}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49e36654-b2c3-47d9-afeb-5db28286a1b0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1Wlhu7LRIp7TbKgaKFQ6Q"}, {"__type__": "91c082Rgh1HBbV2vKQl2J1W", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 63}, "maxSpeed": 10, "acceleration": 10, "brakeDeceleration": 200, "turnSpeed": 50, "friction": 1.5, "initAngle": 0, "maxHealth": 30, "healthBar": null, "destroyedSprite": {"__uuid__": "86087b4b-4284-4b91-8707-fa1f71406ea1@f9941", "__expectedType__": "cc.SpriteFrame"}, "removeDelay": 3, "paintPrefab": {"__uuid__": "a46287ff-f74b-4075-a483-08b1027fe72d", "__expectedType__": "cc.Prefab"}, "paintSprayInterval": 0.2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64VaHtchFMh4Ez7APHyGJG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50sOnQ8Y1KcKSuVZQeN0qR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "car-3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [{"__id__": 66}], "_active": true, "_components": [{"__id__": 78}, {"__id__": 80}, {"__id__": 82}, {"__id__": 84}, {"__id__": 86}], "_prefab": {"__id__": 88}, "_lpos": {"__type__": "cc.Vec3", "x": 610.325, "y": -338.674, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.**********865475, "w": 0.**********865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 2, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 65}, "_prefab": {"__id__": 67}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 66}, "asset": {"__uuid__": "5fa4a020-ad12-4d51-9e7d-57cecdc056ca", "__expectedType__": "cc.Prefab"}, "fileId": "40tQNkNPtP4aURzO7VMMIR", "instance": {"__id__": 68}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "00zwVdEFlGoLnPSiR6hmTZ", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 69}, {"__id__": 71}, {"__id__": 72}, {"__id__": 73}, {"__id__": 74}, {"__id__": 76}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["_name"], "value": "healthBar"}, {"__type__": "cc.TargetInfo", "localID": ["40tQNkNPtP4aURzO7VMMIR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 35, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.**********865475, "w": 0.**********865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 75}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 300, "height": 15}}, {"__type__": "cc.TargetInfo", "localID": ["27NHqliW9Fb4bDUyccWJj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_progress"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 79}, "_contentSize": {"__type__": "cc.Size", "width": 27.8, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77v2PHeqpISLHCDO/8C5P3"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 81}, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 1, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5N+gccBdF1ItHQOpZ8uXt"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 83}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 1, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 27.8, "height": 60.1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "279Oh2IrxGZ43nUuERd9jS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 85}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49e36654-b2c3-47d9-afeb-5db28286a1b0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aeXo01Po9HQKvz3eQLwO4+"}, {"__type__": "91c082Rgh1HBbV2vKQl2J1W", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 87}, "maxSpeed": 10, "acceleration": 10, "brakeDeceleration": 200, "turnSpeed": 50, "friction": 1.5, "initAngle": 0, "maxHealth": 30, "healthBar": null, "destroyedSprite": {"__uuid__": "86087b4b-4284-4b91-8707-fa1f71406ea1@f9941", "__expectedType__": "cc.SpriteFrame"}, "removeDelay": 3, "paintPrefab": {"__uuid__": "35418bab-1e0a-4108-a267-ce8c8c2422c3", "__expectedType__": "cc.Prefab"}, "paintSprayInterval": 0.2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1b379tXNNLIqa2czBfe4fo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afi6VUYD9DcZcYwA/ar5mp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "car-4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [{"__id__": 90}], "_active": true, "_components": [{"__id__": 102}, {"__id__": 104}, {"__id__": 106}, {"__id__": 108}, {"__id__": 110}], "_prefab": {"__id__": 112}, "_lpos": {"__type__": "cc.Vec3", "x": -610.157, "y": -338.674, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": -0.**********865475, "w": 0.**********865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 2, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -90}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 89}, "_prefab": {"__id__": 91}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "5fa4a020-ad12-4d51-9e7d-57cecdc056ca", "__expectedType__": "cc.Prefab"}, "fileId": "40tQNkNPtP4aURzO7VMMIR", "instance": {"__id__": 92}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "398EY9QyxNvoEw6E0YVYth", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 93}, {"__id__": 95}, {"__id__": 96}, {"__id__": 97}, {"__id__": 98}, {"__id__": 100}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_name"], "value": "healthBar"}, {"__type__": "cc.TargetInfo", "localID": ["40tQNkNPtP4aURzO7VMMIR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -35, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.**********865475, "w": 0.**********865476}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 99}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 300, "height": 15}}, {"__type__": "cc.TargetInfo", "localID": ["27NHqliW9Fb4bDUyccWJj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 101}, "propertyPath": ["_progress"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 103}, "_contentSize": {"__type__": "cc.Size", "width": 27.8, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fZ46VYFJEy7Y/2MFK5FDd"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 105}, "enabledContactListener": true, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 1, "_allowSleep": true, "_gravityScale": 0, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eRuSxKudDIpl/xUdy+cwp"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 107}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 1, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 27.8, "height": 60.1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82C4SiiX9FmZS/Pxd3gdJt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 109}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49e36654-b2c3-47d9-afeb-5db28286a1b0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eQI1JAkdCjKO5sGANMO+o"}, {"__type__": "91c082Rgh1HBbV2vKQl2J1W", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 111}, "maxSpeed": 10, "acceleration": 10, "brakeDeceleration": 200, "turnSpeed": 50, "friction": 1.5, "initAngle": 0, "maxHealth": 30, "healthBar": null, "destroyedSprite": {"__uuid__": "86087b4b-4284-4b91-8707-fa1f71406ea1@f9941", "__expectedType__": "cc.SpriteFrame"}, "removeDelay": 3, "paintPrefab": {"__uuid__": "4666d6c8-2376-4df4-be28-d66e383baace", "__expectedType__": "cc.Prefab"}, "paintSprayInterval": 0.2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9tgjNQWBLa6HJdzgYPHBS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2It3rwSVAYonZr0sxJiGz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 114}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55QaEEFuxE8qPGg1d77psJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54YmnXlspLyaBggn+ZgMJZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "walls", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 117}, {"__id__": 123}, {"__id__": 129}, {"__id__": 135}, {"__id__": 141}, {"__id__": 147}, {"__id__": 153}], "_active": true, "_components": [{"__id__": 159}], "_prefab": {"__id__": 161}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 0, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "tree", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 808.9665, "y": 555.761, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": {"__id__": 119}, "_contentSize": {"__type__": "cc.Size", "width": 1716.892, "height": 974.032}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05lIk6XpVAt4bPc7KUBpCY"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 117}, "_enabled": true, "__prefab": {"__id__": 121}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -2436.2, "y": 405.90000000000003}, "_points": [{"__type__": "cc.Vec2", "x": 1924.2, "y": -421.8}, {"__type__": "cc.Vec2", "x": 1786.4, "y": 94.5}, {"__type__": "cc.Vec2", "x": 3308.4, "y": 99.3}, {"__type__": "cc.Vec2", "x": 3321.7, "y": -676.3}, {"__type__": "cc.Vec2", "x": 2808, "y": -654.9}, {"__type__": "cc.Vec2", "x": 2773.3, "y": -564.3}, {"__type__": "cc.Vec2", "x": 3243.1, "y": -166.8}, {"__type__": "cc.Vec2", "x": 2714.4, "y": -306.2}, {"__type__": "cc.Vec2", "x": 2659.6, "y": -201.2}, {"__type__": "cc.Vec2", "x": 2344.7, "y": -377.2}, {"__type__": "cc.Vec2", "x": 2402.1, "y": -522.5}, {"__type__": "cc.Vec2", "x": 2349.3, "y": -561.8}, {"__type__": "cc.Vec2", "x": 2119.6, "y": -506.7}, {"__type__": "cc.Vec2", "x": 2035.8, "y": -397.6}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2zl2QmWVHvqs5fgUXm/RO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64Ukp8RBFJkIURGWUcvwar", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tree-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 124}, {"__id__": 126}], "_prefab": {"__id__": 128}, "_lpos": {"__type__": "cc.Vec3", "x": -825.478, "y": 561.887, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 125}, "_contentSize": {"__type__": "cc.Size", "width": 1716.892, "height": 974.032}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fkMQVQt1M+KinCxOdbNEl"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 127}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -2436.2, "y": 405.90000000000003}, "_points": [{"__type__": "cc.Vec2", "x": 1577.5, "y": -507.2}, {"__type__": "cc.Vec2", "x": 1581.4, "y": 82.7}, {"__type__": "cc.Vec2", "x": 3410, "y": 74.4}, {"__type__": "cc.Vec2", "x": 3479.4, "y": -182.7}, {"__type__": "cc.Vec2", "x": 3244.1, "y": -278.5}, {"__type__": "cc.Vec2", "x": 3091.3, "y": -150.4}, {"__type__": "cc.Vec2", "x": 2801.4, "y": -394.2}, {"__type__": "cc.Vec2", "x": 2688, "y": -363.6}, {"__type__": "cc.Vec2", "x": 2625.1, "y": -219}, {"__type__": "cc.Vec2", "x": 2424, "y": -263.1}, {"__type__": "cc.Vec2", "x": 2362.9, "y": -449.7}, {"__type__": "cc.Vec2", "x": 2033.6, "y": -671.9}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c61lcpMwdLLLpcWWFbVgpR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55nbG1CfdH+6p5O0fEE0Pd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tree-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 130}, {"__id__": 132}], "_prefab": {"__id__": 134}, "_lpos": {"__type__": "cc.Vec3", "x": 531.319, "y": -518.028, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 131}, "_contentSize": {"__type__": "cc.Size", "width": 1716.892, "height": 974.032}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15hyUsDoVL4Kb3QTZn/IZE"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 133}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 633.1, "y": 392.7}, "_size": {"__type__": "cc.Size", "width": 450.7, "height": 188.6}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dX1JiuENProwkk+yVG9zK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8sWI/M5dJTL+W5b0uxnt+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tree-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 136}, {"__id__": 138}], "_prefab": {"__id__": 140}, "_lpos": {"__type__": "cc.Vec3", "x": -699.4635000000001, "y": 451.829, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": {"__id__": 137}, "_contentSize": {"__type__": "cc.Size", "width": 163.723, "height": 138.28800000000015}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dhD4ztkVGR5KCLO1dZGjY"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 135}, "_enabled": true, "__prefab": {"__id__": 139}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 3, "y": -2.2}, "_size": {"__type__": "cc.Size", "width": 167.3, "height": 138.9}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06E0JJWBpI240fNyzVHKlN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94ljXWkItMWICRVxMagIld", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tree-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 142}, {"__id__": 144}], "_prefab": {"__id__": 146}, "_lpos": {"__type__": "cc.Vec3", "x": -357.728, "y": 430.396, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 143}, "_contentSize": {"__type__": "cc.Size", "width": 163.723, "height": 138.28800000000015}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bS9/lOb9M0K+/q6KW1wLU"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 145}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -1.4, "y": -3}, "_size": {"__type__": "cc.Size", "width": 159.5, "height": 115.6}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2erEYKHpBIeb2nu07xzUQy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3TGk51klLl4cgAWI+ttil", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tree-005", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 148}, {"__id__": 150}], "_prefab": {"__id__": 152}, "_lpos": {"__type__": "cc.Vec3", "x": -189.833, "y": 489.338, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 149}, "_contentSize": {"__type__": "cc.Size", "width": 163.723, "height": 138.28800000000015}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7YrIyqfhC/6Czjr0OPa+2"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 151}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0.5, "y": -3}, "_size": {"__type__": "cc.Size", "width": 121.4, "height": 137.3}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13TS0Xay1HO6WBq+7ip3Ys"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6khndTCxLKp99Z2F/cmUL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tree-006", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 116}, "_children": [], "_active": true, "_components": [{"__id__": 154}, {"__id__": 156}], "_prefab": {"__id__": 158}, "_lpos": {"__type__": "cc.Vec3", "x": 1.282, "y": 433.968, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 155}, "_contentSize": {"__type__": "cc.Size", "width": 163.723, "height": 138.28800000000015}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dAJgJ4GpDULPQyenr26OQ"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 153}, "_enabled": true, "__prefab": {"__id__": 157}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -8.6, "y": -0.6}, "_size": {"__type__": "cc.Size", "width": 137.2, "height": 140.4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5fmSUjWtEeZXDT+RU0UsX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06TosbnJ5CrI+BlpXqCrRl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 160}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34hM4zoHdCUqX6x/HyY/ew"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7euHjDGM1FV7shYpfzGOci", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 163}, "_contentSize": {"__type__": "cc.Size", "width": 3377, "height": 2105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2b0ALzXYtJPo0X8C3Xa/1a"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 165}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d0ade2fb-557f-4edb-a96c-d68e19c7c942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79uc5hPb9BjJZW5PetmcIM"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 167}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -1.3, "y": 1151.5}, "_size": {"__type__": "cc.Size", "width": 3380.2, "height": 196}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cyAmiMBRE+6g7LbrLDhAi"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 169}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -0.1, "y": -1138.6}, "_size": {"__type__": "cc.Size", "width": 3378, "height": 166.2}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90i9i1N2xGr7Y0i102K+Kd"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 171}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -1765.4, "y": 0}, "_size": {"__type__": "cc.Size", "width": 149.2, "height": 2105.4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38Ke9wT4JLy6aX4lrvtwau"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 173}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 1847.5, "y": 0.3}, "_size": {"__type__": "cc.Size", "width": 320.1, "height": 2105.4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dccW0EEldDp6vuUIynuW0Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48dRzRqnRN2bkSv9uyUwDd", "instance": null, "targetOverrides": [{"__id__": 175}, {"__id__": 178}, {"__id__": 181}, {"__id__": 184}, {"__id__": 187}, {"__id__": 189}, {"__id__": 191}, {"__id__": 193}], "nestedPrefabInstanceRoots": [{"__id__": 90}, {"__id__": 66}, {"__id__": 42}, {"__id__": 16}, {"__id__": 2}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 16}, "sourceInfo": {"__id__": 176}, "propertyPath": ["_barSprite"], "target": {"__id__": 16}, "targetInfo": {"__id__": 177}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetInfo", "localID": ["e4T3FcLEZKR5ceOsPb+eF1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 42}, "sourceInfo": {"__id__": 179}, "propertyPath": ["_barSprite"], "target": {"__id__": 42}, "targetInfo": {"__id__": 180}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetInfo", "localID": ["e4T3FcLEZKR5ceOsPb+eF1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 66}, "sourceInfo": {"__id__": 182}, "propertyPath": ["_barSprite"], "target": {"__id__": 66}, "targetInfo": {"__id__": 183}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetInfo", "localID": ["e4T3FcLEZKR5ceOsPb+eF1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 90}, "sourceInfo": {"__id__": 185}, "propertyPath": ["_barSprite"], "target": {"__id__": 90}, "targetInfo": {"__id__": 186}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetInfo", "localID": ["e4T3FcLEZKR5ceOsPb+eF1"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 38}, "sourceInfo": null, "propertyPath": ["healthBar"], "target": {"__id__": 16}, "targetInfo": {"__id__": 188}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 62}, "sourceInfo": null, "propertyPath": ["healthBar"], "target": {"__id__": 42}, "targetInfo": {"__id__": 190}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 86}, "sourceInfo": null, "propertyPath": ["healthBar"], "target": {"__id__": 66}, "targetInfo": {"__id__": 192}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 110}, "sourceInfo": null, "propertyPath": ["healthBar"], "target": {"__id__": 90}, "targetInfo": {"__id__": 194}}, {"__type__": "cc.TargetInfo", "localID": ["e9cQsECkREsqrYHJQVVWOE"]}]