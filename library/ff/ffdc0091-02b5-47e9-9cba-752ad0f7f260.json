[{"__type__": "cc.Prefab", "_name": "bullet", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "bullet", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 4, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_contentSize": {"__type__": "cc.Size", "width": 15, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cbtBk4jRNX6ed0WVOqeL1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2e63d6db-c814-4994-99aa-22ba87c907be@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3mwrWJdRLLq6lcvJ4muo0"}, {"__type__": "91ffczWtpJKh6L+Ua3qo7Ip", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 7}, "speed": 50, "damage": 5, "bulletType": 0, "lifeTime": 1, "explosionPrefab": {"__uuid__": "124515d6-c01d-4b24-a978-eefc47e30c4d", "__expectedType__": "cc.Prefab"}, "explosionRadius": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dd+CZkI+xB9pPwMl6qkkM+"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 9}, "enabledContactListener": true, "bullet": true, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aa1pr+ylBTY7CvLmOqrpP"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 11}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": -0.1, "y": 0}, "_size": {"__type__": "cc.Size", "width": 14.9, "height": 30.2}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3oeF8241IFZ22eomt2a41"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81/HPjmJ1Mz7rPzVFz0YOJ", "instance": null, "targetOverrides": null}]